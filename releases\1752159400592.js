"use strict";var x=Object.defineProperty;var P=(s,e,t)=>e in s?x(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var y=(s,e,t)=>P(s,typeof e!="symbol"?e+"":e,t);const g=require("lodash");var c=(s=>(s.MainBase="Spawn1",s))(c||{}),d=(s=>(s.MainRoom="E49S54",s.TargetRoomFlag="TargetRoom",s))(d||{});Object.values(c);const f={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡",repairing:"🔧"},m=(s,e,t={})=>{const{time:o=Game.time}=t;o%s===0&&e()},K=(s,e,t=!0)=>{const o={road:1,plain:2,swamp:10};let n=0,r=0;for(const l of s)if(l===MOVE)n++;else{if(l===CARRY&&!t)continue;r++}const i=r*o[e],a=n*2,u=a/(i||1),E=Math.ceil(1/u);return{movePerTick:Math.min(u,1),ticksPerMove:E,fatiguePerTick:i,fatigueRecover:a,moveCount:n,fatigueParts:r}};function V(s,e,t=1,o=Game.rooms[d.MainRoom]){if(!o)return[];const n={};return o.lookAtArea(e-t,s-t,e+t,s+t,!0).forEach(r=>{if(n[`${r.x},${r.y}`]!==!1){if(n[`${r.x},${r.y}`]===void 0&&(n[`${r.x},${r.y}`]=!0),r.type==="terrain"&&r.terrain==="wall"){n[`${r.x},${r.y}`]=!1;return}if(r.type==="creep"){n[`${r.x},${r.y}`]=!1;return}if(r.type==="structure"&&r.structure){!(r.structure instanceof StructureContainer)&&!(r.structure instanceof StructureRoad)&&!(r.structure instanceof StructureRampart)&&(n[`${r.x},${r.y}`]=!1);return}}}),Object.entries(n).filter(([r,i])=>i===!0).map(([r])=>{const[i,a]=r.split(",");return{x:Number(i),y:Number(a)}})}function B(s,e,t){return e==="miner"?t?s.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):e==="container"?t?s.pos.findClosestByRange(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):e==="storage"?t?s.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):e==="source"?t?s.pos.findClosestByRange(FIND_SOURCES,{filter:o=>o.energy>0}):s.room.find(FIND_SOURCES,{filter:o=>o.energy>0}):e==="mineral"?t?s.pos.findClosestByRange(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):s.room.find(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):e==="ruin"?t?s.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="tombstone"?t?s.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="resource"?t?s.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):s.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}class R{constructor(e){y(this,"role");this.role=e}getEnergyFromStore(e,t){var n,r;let o=null;if(t.length===1){const[i]=t;i&&(o=B(e,i,!0))}else{const i=[];for(const u of t){const E=(r=(n=B(e,u,!1))==null?void 0:n.filter(l=>l!==null))!=null?r:[];for(const l of E)l&&i.push(l)}let a=[];if(a=i.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),a.length>0&&(o=e.pos.findClosestByRange(a)),o||(a=i.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=e.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=e.pos.findClosestByRange(a))),!o){const u=i.filter(l=>l instanceof Creep&&l.memory&&l.memory.role==="miner"&&l.store[RESOURCE_ENERGY]>0),E=u.filter(l=>l.store.getFreeCapacity(RESOURCE_ENERGY)===0);E.length>0?o=e.pos.findClosestByRange(E):u.length>0&&(o=e.pos.findClosestByRange(u))}o||(a=i.filter(u=>e.pos.isNearTo(u.pos)?!0:u instanceof Source&&V(u.pos.x,u.pos.y,1,e.room).length>1),a.length>0&&(o=e.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof Mineral||u instanceof Deposit),a.length>0&&(o=e.pos.findClosestByRange(a)))}if(o){if(!o.pos.isNearTo(e.pos))return e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}}),o;if(o instanceof Creep&&o.memory.role==="miner")return o;if(o instanceof Source||o instanceof Mineral)return e.harvest(o),m(10,()=>e.say(f.harvesting),{time:e.ticksToLive}),o;if(o instanceof Ruin||o instanceof Tombstone)return e.withdraw(o,RESOURCE_ENERGY),m(10,()=>e.say(f.withdrawing),{time:e.ticksToLive}),o;if(o instanceof StructureStorage||o instanceof StructureContainer)return e.withdraw(o,RESOURCE_ENERGY),m(10,()=>e.say(f.withdrawing),{time:e.ticksToLive}),o;if(o instanceof Resource)return e.pickup(o),m(10,()=>e.say(f.picking),{time:e.ticksToLive}),o}return o}}y(R,"generatorRoleBody",e=>e.reduce((t,{body:o,count:n})=>t.concat(Array(n).fill(o)),[]));const M=[STRUCTURE_TOWER,STRUCTURE_RAMPART,STRUCTURE_WALL,STRUCTURE_ROAD],C=class C extends R{constructor(){super(C.role)}create(e){const{baseId:t=c.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=e,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:r})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="repairing"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_STRUCTURES,{filter:n=>!!(M.includes(n.structureType)&&n.hits<n.hitsMax||n instanceof StructureTower&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((n,r)=>{const i=M.indexOf(n.structureType),a=M.indexOf(r.structureType);return i!==a?i-a:n.hits-r.hits});if(!t.length)return;const o=t[0];if(!o.pos.isNearTo(e))e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});else if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(o,RESOURCE_ENERGY)===OK&&m(5,()=>e.say(f.transferring));else switch(e.repair(o)){case ERR_NOT_IN_RANGE:{e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};y(C,"role","repairer");let k=C;const W=new k,D=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],S=class S extends R{constructor(){super(S.role)}create(e){const{baseId:t=c.MainBase,body:o,name:n,memoryRoleOpts:r={role:"builder",task:"harvesting"}}=e,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:r})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="building"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,n)=>{const r=D.indexOf(o.structureType),i=D.indexOf(n.structureType);return r-i});if(t.length>0)switch(e.memory.task==="repairing"&&(e.memory.task="building"),e.build(t[0])){case ERR_NOT_IN_RANGE:{e.moveTo(t[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{m(10,()=>e.say(f.building),{time:e.ticksToLive});break}}else e.memory.task="repairing",this.roleTask2(e)}roleTask2(e){W.run(e)}};y(S,"role","builder");let _=S;const L=new _,N=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],T=class T extends R{constructor(){super(T.role);y(this,"create",t=>{const{baseId:o=c.MainBase,body:n,name:r,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=t,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(n,a,{memory:i})})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task==="transferring"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="transferring"),t.memory.task==="harvesting"?this.harvestTask(t):this.roleTask(t)}roleTask(t,o=t.room){const n=o.find(FIND_MY_STRUCTURES,{filter:r=>N.includes(r.structureType)&&"store"in r&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((r,i)=>{if(r.structureType===i.structureType)return r.pos.getRangeTo(t.pos)-i.pos.getRangeTo(t.pos);const a=N.indexOf(r.structureType),u=N.indexOf(i.structureType);return a-u});if(n.length>0)switch(t.transfer(n[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{t.moveTo(n[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:m(10,()=>t.say(f.transferring),{time:t.ticksToLive})}}harvestTask(t){const o=["resource","ruin","tombstone","container","miner"];t.body.some(n=>n.type===WORK)&&o.push("source"),this.getEnergyFromStore(t,o)}};y(T,"role","harvester");let v=T;const $=new v,O=class O extends R{constructor(){super(O.role)}create(e){const{baseId:t=c.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"moving"}}=e,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:r})}run(e){e.memory.task==="moving"?this.moveTask(e):this.roleTask(e)}moveTask(e){var o;let t=null;e.memory.targetId?t=Game.getObjectById(e.memory.targetId):t=(o=Memory.sources.Source.map(r=>Game.getObjectById(r)).filter(r=>r!==null).find(r=>r.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==e.name}).length===0))!=null?o:null,t&&(e.pos.isNearTo(t)?(e.memory.task="mining",e.memory.targetId=t.id):(e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}}),m(10,()=>e.say(f.moving))))}roleTask(e){const t=e.pos.findInRange(FIND_MY_CREEPS,1,{filter:o=>o.memory.role!=="miner"&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of t)e.store[RESOURCE_ENERGY]>0&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(e.transfer(o,RESOURCE_ENERGY),m(10,()=>e.say(f.transferring)));if(e.memory.targetId){const o=Game.getObjectById(e.memory.targetId);o&&(e.harvest(o),m(10,()=>e.say(f.mining)));return}e.memory.task="moving"}};y(O,"role","miner");let U=O;const j=new U,h=class h extends R{constructor(){super(h.role);y(this,"create",t=>{const{baseId:o=c.MainBase,body:n,name:r,memoryRoleOpts:i={role:"pioneer",task:"harvesting"}}=t,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(n,a,{memory:i})})}run(t){var o;t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&(t.room.name===((o=t.memory)==null?void 0:o.targetRoomName)&&t.memory.task==="harvesting"?t.memory.task="building":t.memory.task="pioneering"),t.store[RESOURCE_ENERGY]>0&&t.memory.task!=="harvesting"&&this.buildingTask(t),t.memory.task==="harvesting"?this.harvestingTask(t):t.memory.task==="building"?this.buildingTask(t):t.memory.task==="pioneering"&&this.roleTask(t)}harvestingTask(t){let o=null;if(t.memory.targetRoomName&&Game.rooms[t.memory.targetRoomName])o=Game.rooms[t.memory.targetRoomName];else{const n=Game.flags[d.TargetRoomFlag];if(!n){this.getEnergyFromStore(t,["source"]);return}if(n.room)o=n.room;else{t.moveTo(n,{visualizePathStyle:{stroke:"#ffaa00"}}),m(10,()=>t.say(f.moving),{time:t.ticksToLive});return}}o&&(t.room.name===o.name?this.getEnergyFromStore(t,["source"]):(t.moveTo(o.find(FIND_SOURCES)[0],{visualizePathStyle:{stroke:"#ffaa00"}}),m(10,()=>t.say(f.moving),{time:t.ticksToLive})),t.memory.targetRoomName=o.name)}buildingTask(t){const o=t.pos.findInRange(FIND_STRUCTURES,1,{filter:r=>r.structureType===STRUCTURE_ROAD}),n=t.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:r=>r.structureType===STRUCTURE_ROAD});if(n.length){const r=t.build(n[0]);r===ERR_NOT_IN_RANGE?t.moveTo(n[0]):r===OK&&m(10,()=>t.say(f.building),{time:t.ticksToLive});return}if(o.length&&o[0].hits<o[0].hitsMax){const r=t.repair(o[0]);r===ERR_NOT_IN_RANGE?t.moveTo(o[0]):r===OK&&m(10,()=>t.say(f.repairing),{time:t.ticksToLive});return}t.memory.task="pioneering"}roleTask(t){$.roleTask(t,Game.rooms[d.MainRoom])}};y(h,"role","pioneer");let G=h;const z=new G,b=class b extends R{constructor(){super(b.role)}create(e){const{baseId:t=c.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=e,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:r})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="upgrading"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.controller;if(!t)return;switch(e.upgradeController(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{m(10,()=>e.say(f.upgrading),{time:e.ticksToLive});break}}}};y(b,"role","upgrader");let I=b;const H=new I,Y={harvester:$,builder:L,miner:j,upgrader:H,repairer:W,pioneer:z};class Q{constructor(e){y(this,"tower");this.tower=e}run(){var e;this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps()||this.healFriendlyCreeps()||this.tower.store[RESOURCE_ENERGY]>((e=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?e:0)*.6&&this.repairStructures())}attackHostileCreeps(){const e=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:t=>t.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK)});if(e.length>0){const t=e.sort((o,n)=>o.hits-o.hitsMax)[0];return this.tower.attack(t),!0}return!1}healFriendlyCreeps(){const e=this.tower.room.find(FIND_MY_CREEPS,{filter:t=>t.hits<t.hitsMax});if(e.length>0){const t=e.sort((o,n)=>o.hits-n.hits)[0];return this.tower.heal(t),!0}return!1}repairStructures(){const e=this.tower.room.find(FIND_STRUCTURES,{filter:t=>t.hits===t.hitsMax?!1:t instanceof StructureRoad&&t.hits<t.hitsMax*.6||t instanceof StructureContainer&&t.hits<t.hitsMax*.6||t instanceof StructureRampart&&t.hits<t.hitsMax*.05||t instanceof StructureWall&&this.tower.pos.getRangeTo(t)<=6?!0:t.hits<1e5});if(e.length>0){const t=e.sort((o,n)=>o instanceof StructureRoad?-1:n instanceof StructureRoad?1:o instanceof StructureContainer?-1:n instanceof StructureContainer?1:o instanceof StructureRampart?-1:n instanceof StructureRampart?1:o.hits-n.hits)[0];return this.tower.repair(t),!0}return!1}}const J=s=>{s.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER}).forEach(t=>{new Q(t).run()})},X=()=>{q(),ee(),Z()},q=()=>{for(let s in Memory.creeps)Game.creeps[s]||(delete Memory.creeps[s],console.log("Clearing non-existing creep memory:",s))},Z=()=>{var e;const s={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const t of Object.values(Game.creeps))t.name.startsWith("Min")||t.memory.role&&(s[t.memory.role]=((e=s[t.memory.role])!=null?e:0)+1);Memory.creepsCount=s},ee=()=>{te()},te=(s=Game.rooms[d.MainRoom])=>{var n,r;if(!s)return;if(!((n=Memory.sources)!=null&&n.Source)){const i=s.find(FIND_SOURCES);g.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((r=Memory.sources)!=null&&r.Mineral)){const i=s.find(FIND_MINERALS);g.set(Memory,"sources.Mineral",i.map(a=>a.id))}const e=s.find(FIND_DROPPED_RESOURCES);g.set(Memory,"sources.Resource",e.map(i=>i.id));const t=s.find(FIND_RUINS);g.set(Memory,"sources.Ruin",t.map(i=>i.id));const o=s.find(FIND_TOMBSTONES);g.set(Memory,"sources.Tombstone",o.map(i=>i.id))},oe=()=>{m(10,re)},re=()=>{const s=Object.values(Game.spawns);for(const e of s)e.spawning?e.room.visual.text(`Spawning:${e.spawning.name}`,{...e.pos,y:e.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):e.room.visual.text(`${e.store[RESOURCE_ENERGY]}`,{...e.pos,y:e.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},ne={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},se={roleMonitor:{harvester:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},ie={roleMonitor:{harvester:{count:6,body:R.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},ae={roleMonitor:{harvester:{count:4,body:R.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},ue={roleMonitor:{harvester:{count:1,body:R.generatorRoleBody([{body:CARRY,count:17},{body:MOVE,count:9}])},pioneer:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:MOVE,count:4}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])}}},Re=[se,ie,ae,ue],me=s=>{for(let e=s;e>=1;e--){const t=Re[e-1];if(t)return t}return ne},le=()=>{fe()},fe=()=>{var o,n,r,i;const s=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.memory.role&&!u.name.startsWith("Min")&&s.set(u.memory.role,((o=s.get(u.memory.role))!=null?o:0)+1)}const e=me((r=(n=Game.rooms[d.MainRoom].controller)==null?void 0:n.level)!=null?r:0),t=s.entries();for(let[a,u]of t)if(e.roleMonitor[a]&&u<e.roleMonitor[a].count){m(10,()=>{var A,w,p;const E=((A=e.roleMonitor[a])==null?void 0:A.body)||[],l={};for(const F of E)l[F]=(l[F]||0)+1;console.log(`${a} 现有:${u} 需要:${(p=(w=e.roleMonitor[a])==null?void 0:w.count)!=null?p:0} Body:${JSON.stringify(l)}`)}),a!=="repairer"?(i=Y[a])==null||i.create({body:e.roleMonitor[a].body}):utils.role2[a].create({baseId:c.MainBase,body:e.roleMonitor[a].body});break}},ye=["MinMiner","MinMiner2"],ce=()=>{Ee()},Ee=()=>{de()&&(ge(),le())},de=()=>{const s=[...ye.map(t=>({name:t,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},{name:"MinPioneer",role:"pioneer"},{name:"MinPioneer2",role:"pioneer"},{name:"MinPioneer3",role:"pioneer"}],e=Object.values(Game.creeps).filter(t=>t.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const t of s){if(t.name.startsWith("MinMiner")&&e>=2)continue;if(!Game.creeps[t.name]){let n=[];switch(t.role){case"miner":{n=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}case"pioneer":{n=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:5},{body:MOVE,count:4}]);break}default:{n=R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}const r=utils.role2[t.role].create({body:n,name:t.name});switch(r){case OK:{console.log(`MiniGroup 孵化${t.name}成功`);break}case ERR_NOT_ENOUGH_ENERGY:{m(5,()=>console.log(`MiniGroup 缺少${t.name}, 能量不足, 等待孵化`));break}case ERR_NOT_ENOUGH_RESOURCES:{m(5,()=>console.log(`MiniGroup 缺少${t.name}, 资源不足, 等待孵化`));break}default:m(5,()=>console.log(`MiniGroup 孵化${t.name}失败`,r));break}return!1}}return!0},ge=()=>{if(!Memory.creepsCount.miner)return;const s=Memory.creepsCount.miner,e=Object.values(Game.creeps).filter(o=>o.name.startsWith("MinMiner")),t=e.slice(0,2-s);for(const o of e)t.includes(o)||(o.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${o.name}`))},Ce=()=>{oe(),X(),ce();const s=Game.rooms[d.MainRoom].find(FIND_HOSTILE_CREEPS);s.length>0&&console.log("有敌人",s)},Se=()=>{var s,e;for(let t in Game.rooms){const o=Game.rooms[t];if((s=o.controller)!=null&&s.my){Ce();for(let n in Game.creeps){let r=Game.creeps[n];r.memory.role&&((e=Y[r.memory.role])==null||e.run(r))}J(o)}}};module.exports={loop:Se};global.utils={role2:Y,ticksPerMove:K};
