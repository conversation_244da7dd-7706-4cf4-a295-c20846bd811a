"use strict";var A=Object.defineProperty;var G=(e,r,t)=>r in e?A(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t;var C=(e,r,t)=>G(e,typeof r!="symbol"?r+"":r,t);const O=require("lodash"),l={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",waiting:"⏳",transferring:"🔄",receiving:"📥",full:"🛑",building:"🚧",upgrading:"⚡",repairing:"🔧"},f=(e,r,t={})=>{const{time:o=Game.time}=t;o%e===0&&r()};var g=(e=>(e.MainBase="Spawn1",e))(g||{}),_=(e=>(e.MainRoom="E49S54",e))(_||{});Object.values(g);const S={getVisualStatus:e=>{var o;const r=e.fatigue,t=`${(o=e.memory.role)==null?void 0:o.slice(0,3)} ${r>0?r:""}`;return e.room.visual.text(t,e.pos.x,e.pos.y-1,{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},create:e=>{var i,y;const{baseId:r=g.MainBase,body:t,name:o,role:n,opts:a}=e,u=o!=null?o:`${n}-${Game.time}`;return(y=(i=Game.spawns)==null?void 0:i[r])==null?void 0:y.spawnCreep(t,u,O.merge({memory:{role:n}},a))}},c=e=>e.reduce((r,{body:t,count:o})=>r.concat(Array(o).fill(t)),[]),F=(e,r,t=1)=>{var y,R;const o={},n=Game.rooms[_.MainRoom];if(!n)return[];const a=[{x:e,y:r}],u=new Set;for(;a.length>0;){const s=a.pop();if(u.has(`${s.x}-${s.y}`))continue;u.add(`${s.x}-${s.y}`);const E=n.lookAtArea(s.y-t,s.x-t,s.y+t,s.x+t,!0).filter(m=>(m==null?void 0:m.terrain)!=="wall"&&m.type!=="source");for(const m of E)if((m==null?void 0:m.type)==="creep"&&m.creep){const d=Game.creeps[(y=m.creep)==null?void 0:y.name];["miner","minerStore"].includes((R=d.memory.role)!=null?R:"")&&a.push({x:m.x,y:m.y})}else u.add(`${m.x}-${m.y}`),o[`${m.x}-${m.y}`]?o[`${m.x}-${m.y}`].push(m):o[`${m.x}-${m.y}`]=[m]}const i=[];return Object.entries(o).forEach(([s,E])=>{if(new Array(...new Set(E)).every(d=>!["creep","structure"].includes(d.type))){const[d,T]=s.split("-");i.push({x:Number(d),y:Number(T)})}}),i};function I(e,r,t){if(r==="minerStore")return t?e.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="minerStore"&&o.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="minerStore"&&o.store[RESOURCE_ENERGY]>0});if(["container","storage"].includes(r))return t?e.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>["storage","container"].includes(o.structureType)&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0}):e.room.find(FIND_MY_STRUCTURES,{filter:o=>["storage","container"].includes(o.structureType)&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});if(["mineral","source"].includes(r)){const o=t?[e.pos.findClosestByRange(FIND_SOURCES,{filter:a=>a.energy>0})]:e.room.find(FIND_SOURCES,{filter:a=>a.energy>0}),n=t?[e.pos.findClosestByRange(FIND_MINERALS,{filter:a=>a.mineralAmount>0})]:e.room.find(FIND_MINERALS,{filter:a=>a.mineralAmount>0});return[...o,...n]}return r==="ruin"?t?e.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):r==="tombstone"?t?e.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):r==="resource"?t?e.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):e.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}const b=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_CONTAINER,STRUCTURE_STORAGE],w=(e,r)=>{var t;if(e.memory.task==="transferring"){if(e.store[RESOURCE_ENERGY]===0){e.memory.task="harvesting";return}const o=e.room.find(FIND_STRUCTURES,{filter:n=>b.includes(n.structureType)&&"store"in n&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((n,a)=>{const u=b.indexOf(n.structureType),i=b.indexOf(a.structureType);return u-i});o.length>0&&e.transfer(o[0],RESOURCE_ENERGY)===ERR_NOT_IN_RANGE&&e.moveTo(o[0],{visualizePathStyle:{stroke:"#ffffff"}});return}if(e.store.getFreeCapacity()===0){e.memory.role==="harvester"&&(e.memory.task="transferring");return}if(e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"){e.memory.task="harvesting";return}if(e.store.getFreeCapacity()>0&&e.memory.task==="harvesting"){if(e.memory.role!=="harvester"){const R=e.room.find(FIND_STRUCTURES,{filter:s=>s.structureType===STRUCTURE_CONTAINER&&s.store[RESOURCE_ENERGY]>e.store.getFreeCapacity()});if(R.length>0)if(e.pos.isNearTo(R[0])){e.withdraw(R[0],RESOURCE_ENERGY)===OK&&e.say(l.receiving);return}else{e.moveTo(R[0],{visualizePathStyle:{stroke:"#ffaa00"}});return}}const n=Object.values(Memory.sources.Source).map(R=>Game.getObjectById(R)).reduce((R,s)=>{var E,m,d,T;return s instanceof Source&&s.energy>0?R.Source=[...(E=R.Source)!=null?E:[],s]:s instanceof Resource&&s.amount>0?R.Resource=[...(m=R.Resource)!=null?m:[],s]:s instanceof Tombstone&&s.store[RESOURCE_ENERGY]>0?R.Tombstone=[...(d=R.Tombstone)!=null?d:[],s]:s instanceof Ruin&&s.store[RESOURCE_ENERGY]>0&&(R.Ruin=[...(T=R.Ruin)!=null?T:[],s]),R},{Source:[],Resource:[],Tombstone:[],Ruin:[]});if(n.Resource.length>0){const R=n.Resource[0],s=e.pickup(R);s===OK?e.say(l.harvesting):s===ERR_NOT_IN_RANGE&&e.moveTo(R,{visualizePathStyle:{stroke:"#ffaa00"}});return}if(e.memory.role!=="harvester"&&(n.Tombstone.length>0||n.Ruin.length>0)){const R=(t=n.Tombstone[0])!=null?t:n.Ruin[0],s=e.withdraw(R,RESOURCE_ENERGY);s===OK?e.say(l.harvesting):s===ERR_NOT_IN_RANGE&&e.moveTo(R,{visualizePathStyle:{stroke:"#ffaa00"}});return}if(e.memory.role!=="harvester"||e.memory.role==="harvester"&&(r==null?void 0:r.priority)==="high"){const R=e.pos.findClosestByPath(FIND_MY_CREEPS,{filter:s=>{if((s.memory.role==="miner"||s.memory.role==="minerStore")&&s.store[RESOURCE_ENERGY]>0){const E=F(s.pos.x,s.pos.y);return(E==null?void 0:E.length)>1}return!1}});if(R&&!e.pos.isNearTo(R)){e.moveTo(R);return}}const{priority:a="low"}=r!=null?r:{},u=n.Source;if(u.length===0)return;const i=a==="high"?u[0]:u[u.length-1];e.memory.targetSourceId=i.id;const y=e.harvest(i);y===OK?f(10,()=>e.say(l.harvesting)):y===ERR_NOT_IN_RANGE&&e.moveTo(i,{visualizePathStyle:{stroke:"#ffaa00"}});return}},p=(e,r={})=>S.create({baseId:e,body:[WORK,WORK,CARRY,MOVE],role:"harvester",opts:{memory:{task:"harvesting"}},...r}),h={run:w,create:p},Y=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],D=(e,r={})=>{const t=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,n)=>{const a=Y.indexOf(o.structureType),u=Y.indexOf(n.structureType);return a-u});if(t.length===0){h.run(e,r),e.memory.task="harvesting";return}if(e.memory.task==="building"&&e.store[RESOURCE_ENERGY]===0&&(f(10,()=>e.say(l.harvesting)),e.memory.task="harvesting"),e.memory.task==="harvesting"&&e.store.getFreeCapacity()===0&&(f(10,()=>e.say(l.building)),e.memory.task="building"),e.memory.task==="harvesting"&&h.run(e,r),e.memory.task==="building"){const o=t.filter(n=>n.progress<n.progressTotal);if(o.length===0){e.memory.task="harvesting";return}e.build(o[0])===ERR_NOT_IN_RANGE&&e.moveTo(o[0],{visualizePathStyle:{stroke:"#ffffff"}})}},x=(e,r={})=>S.create({baseId:e,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE],role:"builder",opts:{memory:{task:"building"}},...r}),K={run:D,create:x},W=e=>{e.store.getFreeCapacity()===0&&f(5,()=>e.say(l.full));const r=e.pos.findInRange(FIND_MY_CREEPS,1).filter(o=>o.store.getFreeCapacity()>0).sort((o,n)=>o.memory.role==="miner"&&n.memory.role!=="miner"?1:o.memory.role!=="miner"&&n.memory.role==="miner"?-1:0);if(r.length>0){const o=r[0];e.transfer(o,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring))}let t=null;if(e.memory.targetId)t=Game.getObjectById(e.memory.targetId);else{const o=Memory.sources.Source.filter(n=>{const a=Game.getObjectById(n);return a.energy>0&&a.ticksToRegeneration<300}).map(n=>Game.getObjectById(n));t=o.length?o.pop():null}if(t){if(e.memory.targetId=t.id,e.memory.task==="mining"){const o=e.harvest(t);o===OK?f(10,()=>e.say(l.mining)):o===ERR_NOT_ENOUGH_RESOURCES&&f(10,()=>e.say(l.waiting))}e.memory.task==="harvesting"&&(e.moveTo(t,{visualizePathStyle:{stroke:"#ffaa00"},reusePath:5}),e.pos.isNearTo(t)&&(e.memory.task="mining"))}},P=(e,r={})=>S.create({baseId:e,body:[WORK,WORK,WORK,WORK,WORK,CARRY,CARRY,MOVE],role:"miner",opts:{memory:{task:"harvesting"}},...r}),$={run:W,create:P},B=e=>{const r=e.pos.findInRange(FIND_MY_CREEPS,1).filter(t=>t.store.getFreeCapacity()>0&&t.memory.role!=="miner").sort(t=>t.memory.role!=="miner"&&t.memory.role!=="minerStore"?-1:0);for(let t of r)e.transfer(t,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring))},V=(e,r={})=>S.create({baseId:e,body:[CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,MOVE],role:"minerStore",...r}),L={run:B,create:V},z=e=>{if(e.memory.task==="repairing"&&e.store[RESOURCE_ENERGY]===0){e.say(l.harvesting),e.memory.task="harvesting";return}if(e.memory.task==="harvesting"){e.store.getFreeCapacity()===0?(e.memory.task="repairing",e.say(l.repairing)):h.run(e);return}if(e.memory.task==="repairing"){const r=e.room.find(FIND_STRUCTURES,{filter:o=>o.hits<o.hitsMax||o.structureType===STRUCTURE_TOWER}).sort((o,n)=>o.structureType===STRUCTURE_TOWER&&n.structureType!==STRUCTURE_TOWER?-1:o.structureType!==STRUCTURE_TOWER&&n.structureType===STRUCTURE_TOWER?1:Math.abs(o.hits-n.hits)>1e3?o.hits-n.hits:0);if(!r.length)return;const t=r[0];if(!t.pos.isNearTo(e)){e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});return}if(t.structureType===STRUCTURE_TOWER&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(t,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring));else switch(e.repair(t)){case ERR_NOT_IN_RANGE:{e.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{f(10,()=>e.say(l.repairing),{time:e.ticksToLive});break}}}},j=(e,r={})=>S.create({baseId:e,body:[WORK,WORK,WORK,CARRY,CARRY,CARRY,MOVE,MOVE],role:"repairer",opts:{memory:{task:"repairing"}},...r}),q={run:z,create:j},H=e=>{if(e.memory.task==="upgrading"&&e.store[RESOURCE_ENERGY]===0){e.say(l.harvesting),e.memory.task="harvesting";return}if(e.memory.task==="harvesting"){e.store.getFreeCapacity()===0?(e.memory.task="upgrading",e.say(l.upgrading)):h.run(e);return}if(e.memory.task==="upgrading"){const r=e.room.controller;if(!r)return;switch(e.upgradeController(r)){case ERR_NOT_IN_RANGE:{e.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{f(10,()=>e.say(l.upgrading),{time:e.ticksToLive});break}}return}},X=(e,r={})=>S.create({baseId:e,body:[WORK,CARRY,CARRY,MOVE,MOVE],role:"upgrader",opts:{memory:{task:"upgrading"}},...r}),J={run:H,create:X},v={builder:K,upgrader:J,miner:$,minerStore:L,repairer:q};class Q{constructor(r){C(this,"role");this.role=r}}const U=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_CONTAINER,STRUCTURE_STORAGE],N=class N extends Q{constructor(){super(N.role);C(this,"task","harvesting");C(this,"create",t=>{const{baseId:o=g.MainBase,body:n,name:a,memoryRoleOpts:u}=t,i=a!=null?a:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(n,i,{memory:u})})}run(t){if(t.store[RESOURCE_ENERGY]===0&&t.memory.task==="transferring"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="transferring"),t.memory.task==="harvesting"){this.harvestTask(t);return}if(t.memory.task==="transferring"){this.roleTask(t);return}}roleTask(t){const o=t.room.find(FIND_MY_STRUCTURES,{filter:n=>U.includes(n.structureType)&&"store"in n&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((n,a)=>{const u=U.indexOf(n.structureType),i=U.indexOf(a.structureType);return u-i});if(o.length>0)switch(t.transfer(o[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{t.moveTo(o[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:f(10,()=>t.say(l.transferring),{time:t.ticksToLive})}}harvestTask(t){const o=t.pos.findClosestByRange(FIND_MY_CREEPS,{filter:n=>n.memory.role==="minerStore"&&n.store[RESOURCE_ENERGY]>0});o&&t.transfer(o,RESOURCE_ENERGY)===OK&&f(10,()=>t.say(l.transferring),{time:t.ticksToLive}),this.getEnergyFromStore(t,["minerStore","source","mineral","deposit"])}getEnergyFromStore(t,o){var a,u;let n=null;if(o.length===1){const[i]=o;i&&(n=I(t,i,!0))}else{const i=[];for(const R of o){const s=(u=(a=I(t,R,!1))==null?void 0:a.filter(E=>E!==null))!=null?u:[];for(const E of s)E&&i.push(E)}let y=1/0;for(const R of i){const s=t.pos.getRangeTo(R);s<y&&(y=s,n=R)}}if(n){if(!n.pos.isNearTo(t.pos)){t.moveTo(n,{visualizePathStyle:{stroke:"#ffffff"}});return}if(n instanceof Creep)return;if(n instanceof Source||n instanceof Mineral){t.harvest(n),f(10,()=>t.say(l.harvesting),{time:t.ticksToLive});return}if(n instanceof Ruin||n instanceof Tombstone){t.withdraw(n,RESOURCE_ENERGY),f(10,()=>t.say(l.withdrawing),{time:t.ticksToLive});return}if(n instanceof StructureStorage||n instanceof StructureContainer){t.withdraw(n,RESOURCE_ENERGY),f(10,()=>t.say(l.withdrawing),{time:t.ticksToLive});return}if(n instanceof Resource){t.pickup(n),f(10,()=>t.say(l.picking),{time:t.ticksToLive});return}}}};C(N,"role","harvester");let M=N;const Z=new M,k={harvester:Z};class ee{constructor(r){C(this,"tower");this.tower=r}run(){this.tower.store[RESOURCE_ENERGY]!==0&&(this.healFriendlyCreeps(),this.attackHostileCreeps(),this.repairStructures())}attackHostileCreeps(){const r=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:t=>t.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK)});if(r.length>0){const t=r.sort((o,n)=>o.hits-o.hitsMax)[0];this.tower.attack(t)}}healFriendlyCreeps(){const r=this.tower.room.find(FIND_MY_CREEPS,{filter:t=>t.hits<t.hitsMax});if(r.length>0){const t=r.sort((o,n)=>o.hits-n.hits)[0];this.tower.heal(t)}}repairStructures(){const r=this.tower.room.find(FIND_STRUCTURES,{filter:t=>t.hits<1e4&&t.hits<t.hitsMax});if(r.length>0){const t=r.sort((o,n)=>o.hits-n.hits)[0];this.tower.repair(t)}}}const te=e=>{e.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER}).forEach(t=>{new ee(t).run()})},re=()=>{oe(),ne()},oe=()=>{for(let e in Memory.creeps)Game.creeps[e]||(delete Memory.creeps[e],console.log("Clearing non-existing creep memory:",e))},ne=()=>{se()};FIND_DROPPED_RESOURCES,FIND_SOURCES,FIND_TOMBSTONES,FIND_RUINS;const se=()=>{var n,a;const e=Game.rooms[_.MainRoom];if(!e)return;if(!((n=Memory.sources)!=null&&n.Source)){const u=e.find(FIND_SOURCES);O.set(Memory,"sources.Source",u.map(i=>i.id))}if(!((a=Memory.sources)!=null&&a.Mineral)){const u=e.find(FIND_MINERALS);O.set(Memory,"sources.Mineral",u.map(i=>i.id))}const r=e.find(FIND_DROPPED_RESOURCES);O.set(Memory,"sources.Resource",r.map(u=>u.id));const t=e.find(FIND_RUINS);O.set(Memory,"sources.Ruin",t.map(u=>u.id));const o=e.find(FIND_TOMBSTONES);O.set(Memory,"sources.Tombstone",o.map(u=>u.id))},ie={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]}}},ae={roleMonitor:{harvester:{count:5,body:c([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:c([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:c([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])}}},Re={roleMonitor:{harvester:{count:6,body:c([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:c([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:c([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:c([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},ue={roleMonitor:{harvester:{count:8,body:c([{body:CARRY,count:10},{body:MOVE,count:6}])},builder:{count:2,body:c([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:10,body:c([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:c([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},me=[ae,Re,ue],le=e=>{for(let r=e;r>=1;r--){const t=me[r-1];if(t)return t}return ie},fe=()=>{ye()},ye=()=>{var o,n,a,u;const e=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let i in Game.creeps){const y=Game.creeps[i];y.memory.role&&(e.set(y.memory.role,((o=e.get(y.memory.role))!=null?o:0)+1),f(10,()=>S.getVisualStatus(y)))}const r=le((a=(n=Game.rooms[_.MainRoom].controller)==null?void 0:n.level)!=null?a:0),t=e.entries();for(let[i,y]of t)if(r.roleMonitor[i]&&y<r.roleMonitor[i].count){f(10,()=>{var E;const R=((E=r.roleMonitor[i])==null?void 0:E.body)||[],s={};for(const m of R)s[m]=(s[m]||0)+1;console.log(`${i} 数量不足`,JSON.stringify(s))}),i==="harvester"?(u=k.harvester)==null||u.create({body:r.roleMonitor[i].body,memoryRoleOpts:{role:i,task:"harvesting"}}):utils.role[i].create(g.MainBase,{body:r.roleMonitor[i].body});break}},Ee=()=>{ce()&&de()&&ge()&&fe()},ce=()=>{const e=[{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},{name:"MinRepairer",role:"repairer"}];for(const r of e)if(!Game.creeps[r.name])return Game.spawns[g.MainBase].spawnCreep(c([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]),r.name,{memory:{role:r.role,task:"harvesting"}}),f(10,()=>console.log(`MinCreepGroup中缺少: ${r.name}, 等待孵化...`)),!1;return!0},de=()=>{const e=[{name:"FixedMiner1",pos:{x:9,y:44},targetId:"5bbcaffd9099fc012e63b77c"},{name:"FixedMiner2",pos:{x:4,y:40},targetId:"5bbcaffd9099fc012e63b77b"}];for(const r of e){const t=Game.creeps[r.name];if(t&&!t.pos.isEqualTo(t.pos.x,t.pos.y))t.moveTo(r.pos.x,r.pos.y);else if(!t)return Game.spawns[g.MainBase].spawnCreep(c([{body:WORK,count:6},{body:CARRY,count:3},{body:MOVE,count:1}]),r.name,{memory:{role:"miner",task:"harvesting",targetId:r.targetId}}),f(10,()=>console.log(`Miner中缺少: ${r.name}, 等待孵化...`)),!1}return!0},ge=()=>{const e=[{name:"MinerStore-2",pos:{x:5,y:39}},{name:"MinerStore-3",pos:{x:10,y:43}}];for(const r of e){const t=Game.creeps[r.name];if(t&&!t.pos.isEqualTo(r.pos.x,r.pos.y))t.moveTo(r.pos.x,r.pos.y);else if(!t)return Game.spawns[g.MainBase].spawnCreep(c([{body:CARRY,count:15},{body:MOVE,count:1}]),r.name,{memory:{role:"minerStore"}}),f(10,()=>console.log(`MinerStore中缺少: ${r.name}, 等待孵化...`)),!1}return!0},Se=()=>{re(),Ee()},Ce=()=>{var e,r,t,o;for(let n in Game.rooms){const a=Game.rooms[n];if((e=a.controller)!=null&&e.my){Se();for(let u in Game.creeps){let i=Game.creeps[u];if(i.memory.role=="harvester"&&((r=k.harvester)==null||r.run(i)),i.memory.role=="builder"){const y=Object.values(Game.creeps).filter(R=>R.memory.role==="builder").findIndex((R,s)=>R.name===i.name&&s<3)!==-1;(t=v.builder)==null||t.run(i,{priority:y?"high":"low"})}i.memory.role&&((o=v[i.memory.role])==null||o.run(i))}te(a)}}};module.exports={loop:Ce};global.utils={role:v};
