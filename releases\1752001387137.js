"use strict";var A=Object.defineProperty;var G=(e,t,o)=>t in e?A(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var O=(e,t,o)=>G(e,typeof t!="symbol"?t+"":t,o);const C=require("lodash"),l={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",waiting:"⏳",transferring:"🔄",receiving:"📥",full:"🛑",building:"🚧",upgrading:"⚡",repairing:"🔧"},f=(e,t,o={})=>{const{time:r=Game.time}=o;r%e===0&&t()};var d=(e=>(e.MainBase="Spawn1",e))(d||{}),_=(e=>(e.MainRoom="E49S54",e))(_||{});Object.values(d);const S={getVisualStatus:e=>{var r;const t=e.fatigue,o=`${(r=e.memory.role)==null?void 0:r.slice(0,3)} ${t>0?t:""}`;return e.room.visual.text(o,e.pos.x,e.pos.y-1,{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},create:e=>{var i,y;const{baseId:t=d.MainBase,body:o,name:r,role:n,opts:u}=e,R=r!=null?r:`${n}-${Game.time}`;return(y=(i=Game.spawns)==null?void 0:i[t])==null?void 0:y.spawnCreep(o,R,C.merge({memory:{role:n}},u))}},E=e=>e.reduce((t,{body:o,count:r})=>t.concat(Array(r).fill(o)),[]),p=(e,t,o=1)=>{var y,a;const r={},n=Game.rooms[_.MainRoom];if(!n)return[];const u=[{x:e,y:t}],R=new Set;for(;u.length>0;){const s=u.pop();if(R.has(`${s.x}-${s.y}`))continue;R.add(`${s.x}-${s.y}`);const c=n.lookAtArea(s.y-o,s.x-o,s.y+o,s.x+o,!0).filter(m=>(m==null?void 0:m.terrain)!=="wall"&&m.type!=="source");for(const m of c)if((m==null?void 0:m.type)==="creep"&&m.creep){const g=Game.creeps[(y=m.creep)==null?void 0:y.name];["miner","minerStore"].includes((a=g.memory.role)!=null?a:"")&&u.push({x:m.x,y:m.y})}else R.add(`${m.x}-${m.y}`),r[`${m.x}-${m.y}`]?r[`${m.x}-${m.y}`].push(m):r[`${m.x}-${m.y}`]=[m]}const i=[];return Object.entries(r).forEach(([s,c])=>{if(new Array(...new Set(c)).every(g=>!["creep","structure"].includes(g.type))){const[g,T]=s.split("-");i.push({x:Number(g),y:Number(T)})}}),i};function k(e,t,o){return t==="minerStore"?o?e.pos.findClosestByRange(FIND_MY_CREEPS,{filter:r=>r.memory.role==="minerStore"&&r.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_MY_CREEPS,{filter:r=>r.memory.role==="minerStore"&&r.store[RESOURCE_ENERGY]>0}):["container","storage"].includes(t)?o?e.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:r=>["storage","container"].includes(r.structureType)&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}):e.room.find(FIND_MY_STRUCTURES,{filter:r=>["storage","container"].includes(r.structureType)&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}):t==="source"?o?[e.pos.findClosestByRange(FIND_SOURCES,{filter:r=>r.energy>0})]:e.room.find(FIND_SOURCES,{filter:r=>r.energy>0}):t==="mineral"?o?[e.pos.findClosestByRange(FIND_MINERALS,{filter:r=>r.mineralAmount>0})]:e.room.find(FIND_MINERALS,{filter:r=>r.mineralAmount>0}):t==="ruin"?o?e.pos.findClosestByRange(FIND_RUINS,{filter:r=>r.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_RUINS,{filter:r=>r.store[RESOURCE_ENERGY]>0}):t==="tombstone"?o?e.pos.findClosestByRange(FIND_TOMBSTONES,{filter:r=>r.store[RESOURCE_ENERGY]>0}):e.room.find(FIND_TOMBSTONES,{filter:r=>r.store[RESOURCE_ENERGY]>0}):t==="resource"?o?e.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:r=>r.amount>0}):e.room.find(FIND_DROPPED_RESOURCES,{filter:r=>r.amount>0}):null}const b=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_CONTAINER,STRUCTURE_STORAGE],w=(e,t)=>{var o;if(e.memory.task==="transferring"){if(e.store[RESOURCE_ENERGY]===0){e.memory.task="harvesting";return}const r=e.room.find(FIND_STRUCTURES,{filter:n=>b.includes(n.structureType)&&"store"in n&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((n,u)=>{const R=b.indexOf(n.structureType),i=b.indexOf(u.structureType);return R-i});r.length>0&&e.transfer(r[0],RESOURCE_ENERGY)===ERR_NOT_IN_RANGE&&e.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});return}if(e.store.getFreeCapacity()===0){e.memory.role==="harvester"&&(e.memory.task="transferring");return}if(e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"){e.memory.task="harvesting";return}if(e.store.getFreeCapacity()>0&&e.memory.task==="harvesting"){if(e.memory.role!=="harvester"){const a=e.room.find(FIND_STRUCTURES,{filter:s=>s.structureType===STRUCTURE_CONTAINER&&s.store[RESOURCE_ENERGY]>e.store.getFreeCapacity()});if(a.length>0)if(e.pos.isNearTo(a[0])){e.withdraw(a[0],RESOURCE_ENERGY)===OK&&e.say(l.receiving);return}else{e.moveTo(a[0],{visualizePathStyle:{stroke:"#ffaa00"}});return}}const n=Object.values(Memory.sources.Source).map(a=>Game.getObjectById(a)).reduce((a,s)=>{var c,m,g,T;return s instanceof Source&&s.energy>0?a.Source=[...(c=a.Source)!=null?c:[],s]:s instanceof Resource&&s.amount>0?a.Resource=[...(m=a.Resource)!=null?m:[],s]:s instanceof Tombstone&&s.store[RESOURCE_ENERGY]>0?a.Tombstone=[...(g=a.Tombstone)!=null?g:[],s]:s instanceof Ruin&&s.store[RESOURCE_ENERGY]>0&&(a.Ruin=[...(T=a.Ruin)!=null?T:[],s]),a},{Source:[],Resource:[],Tombstone:[],Ruin:[]});if(n.Resource.length>0){const a=n.Resource[0],s=e.pickup(a);s===OK?e.say(l.harvesting):s===ERR_NOT_IN_RANGE&&e.moveTo(a,{visualizePathStyle:{stroke:"#ffaa00"}});return}if(e.memory.role!=="harvester"&&(n.Tombstone.length>0||n.Ruin.length>0)){const a=(o=n.Tombstone[0])!=null?o:n.Ruin[0],s=e.withdraw(a,RESOURCE_ENERGY);s===OK?e.say(l.harvesting):s===ERR_NOT_IN_RANGE&&e.moveTo(a,{visualizePathStyle:{stroke:"#ffaa00"}});return}if(e.memory.role!=="harvester"||e.memory.role==="harvester"&&(t==null?void 0:t.priority)==="high"){const a=e.pos.findClosestByPath(FIND_MY_CREEPS,{filter:s=>{if((s.memory.role==="miner"||s.memory.role==="minerStore")&&s.store[RESOURCE_ENERGY]>0){const c=p(s.pos.x,s.pos.y);return(c==null?void 0:c.length)>1}return!1}});if(a&&!e.pos.isNearTo(a)){e.moveTo(a);return}}const{priority:u="low"}=t!=null?t:{},R=n.Source;if(R.length===0)return;const i=u==="high"?R[0]:R[R.length-1];e.memory.targetSourceId=i.id;const y=e.harvest(i);y===OK?f(10,()=>e.say(l.harvesting)):y===ERR_NOT_IN_RANGE&&e.moveTo(i,{visualizePathStyle:{stroke:"#ffaa00"}});return}},F=(e,t={})=>S.create({baseId:e,body:[WORK,WORK,CARRY,MOVE],role:"harvester",opts:{memory:{task:"harvesting"}},...t}),h={run:w,create:F},Y=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],x=(e,t={})=>{const o=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((r,n)=>{const u=Y.indexOf(r.structureType),R=Y.indexOf(n.structureType);return u-R});if(o.length===0){h.run(e,t),e.memory.task="harvesting";return}if(e.memory.task==="building"&&e.store[RESOURCE_ENERGY]===0&&(f(10,()=>e.say(l.harvesting)),e.memory.task="harvesting"),e.memory.task==="harvesting"&&e.store.getFreeCapacity()===0&&(f(10,()=>e.say(l.building)),e.memory.task="building"),e.memory.task==="harvesting"&&h.run(e,t),e.memory.task==="building"){const r=o.filter(n=>n.progress<n.progressTotal);if(r.length===0){e.memory.task="harvesting";return}e.build(r[0])===ERR_NOT_IN_RANGE&&e.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}})}},D=(e,t={})=>S.create({baseId:e,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE],role:"builder",opts:{memory:{task:"building"}},...t}),W={run:x,create:D},K=e=>{e.store.getFreeCapacity()===0&&f(5,()=>e.say(l.full));const t=e.pos.findInRange(FIND_MY_CREEPS,1).filter(r=>r.store.getFreeCapacity()>0).sort((r,n)=>r.memory.role==="miner"&&n.memory.role!=="miner"?1:r.memory.role!=="miner"&&n.memory.role==="miner"?-1:0);if(t.length>0){const r=t[0];e.transfer(r,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring))}let o=null;if(e.memory.targetId)o=Game.getObjectById(e.memory.targetId);else{const r=Memory.sources.Source.filter(n=>{const u=Game.getObjectById(n);return u.energy>0&&u.ticksToRegeneration<300}).map(n=>Game.getObjectById(n));o=r.length?r.pop():null}if(o){if(e.memory.targetId=o.id,e.memory.task==="mining"){const r=e.harvest(o);r===OK?f(10,()=>e.say(l.mining)):r===ERR_NOT_ENOUGH_RESOURCES&&f(10,()=>e.say(l.waiting))}e.memory.task==="harvesting"&&(e.moveTo(o,{visualizePathStyle:{stroke:"#ffaa00"},reusePath:5}),e.pos.isNearTo(o)&&(e.memory.task="mining"))}},P=(e,t={})=>S.create({baseId:e,body:[WORK,WORK,WORK,WORK,WORK,CARRY,CARRY,MOVE],role:"miner",opts:{memory:{task:"harvesting"}},...t}),$={run:K,create:P},B=e=>{const t=e.pos.findInRange(FIND_MY_CREEPS,1).filter(o=>o.store.getFreeCapacity()>0&&o.memory.role!=="miner").sort(o=>o.memory.role!=="miner"&&o.memory.role!=="minerStore"?-1:0);for(let o of t)e.transfer(o,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring))},V=(e,t={})=>S.create({baseId:e,body:[CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,CARRY,MOVE],role:"minerStore",...t}),L={run:B,create:V},z=e=>{if(e.memory.task==="repairing"&&e.store[RESOURCE_ENERGY]===0){e.say(l.harvesting),e.memory.task="harvesting";return}if(e.memory.task==="harvesting"){e.store.getFreeCapacity()===0?(e.memory.task="repairing",e.say(l.repairing)):h.run(e);return}if(e.memory.task==="repairing"){const t=e.room.find(FIND_STRUCTURES,{filter:r=>r.hits<r.hitsMax||r.structureType===STRUCTURE_TOWER}).sort((r,n)=>r.structureType===STRUCTURE_TOWER&&n.structureType!==STRUCTURE_TOWER?-1:r.structureType!==STRUCTURE_TOWER&&n.structureType===STRUCTURE_TOWER?1:Math.abs(r.hits-n.hits)>1e3?r.hits-n.hits:0);if(!t.length)return;const o=t[0];if(!o.pos.isNearTo(e)){e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});return}if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(o,RESOURCE_ENERGY)===OK&&f(5,()=>e.say(l.transferring));else switch(e.repair(o)){case ERR_NOT_IN_RANGE:{e.moveTo(t[0],{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{f(10,()=>e.say(l.repairing),{time:e.ticksToLive});break}}}},j=(e,t={})=>S.create({baseId:e,body:[WORK,WORK,WORK,CARRY,CARRY,CARRY,MOVE,MOVE],role:"repairer",opts:{memory:{task:"repairing"}},...t}),q={run:z,create:j},H=e=>{if(e.memory.task==="upgrading"&&e.store[RESOURCE_ENERGY]===0){e.say(l.harvesting),e.memory.task="harvesting";return}if(e.memory.task==="harvesting"){e.store.getFreeCapacity()===0?(e.memory.task="upgrading",e.say(l.upgrading)):h.run(e);return}if(e.memory.task==="upgrading"){const t=e.room.controller;if(!t)return;switch(e.upgradeController(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{f(10,()=>e.say(l.upgrading),{time:e.ticksToLive});break}}return}},X=(e,t={})=>S.create({baseId:e,body:[WORK,CARRY,CARRY,MOVE,MOVE],role:"upgrader",opts:{memory:{task:"upgrading"}},...t}),J={run:H,create:X},U={builder:W,upgrader:J,miner:$,minerStore:L,repairer:q};class Q{constructor(t){O(this,"role");this.role=t}getEnergyFromStore(t,o){var n,u;let r=null;if(o.length===1){const[R]=o;R&&(r=k(t,R,!0))}else{const R=[];for(const y of o){const a=(u=(n=k(t,y,!1))==null?void 0:n.filter(s=>s!==null))!=null?u:[];for(const s of a)s&&R.push(s)}let i=1/0;for(const y of R){const a=t.pos.getRangeTo(y);a<i&&(i=a,r=y)}}if(r){if(!r.pos.isNearTo(t.pos)){t.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}});return}if(r instanceof Creep)return;if(r instanceof Source||r instanceof Mineral){t.harvest(r),f(10,()=>t.say(l.harvesting),{time:t.ticksToLive});return}if(r instanceof Ruin||r instanceof Tombstone){t.withdraw(r,RESOURCE_ENERGY),f(10,()=>t.say(l.withdrawing),{time:t.ticksToLive});return}if(r instanceof StructureStorage||r instanceof StructureContainer){t.withdraw(r,RESOURCE_ENERGY),f(10,()=>t.say(l.withdrawing),{time:t.ticksToLive});return}if(r instanceof Resource){t.pickup(r),f(10,()=>t.say(l.picking),{time:t.ticksToLive});return}}}}const v=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_CONTAINER,STRUCTURE_STORAGE],N=class N extends Q{constructor(){super(N.role);O(this,"create",o=>{const{baseId:r=d.MainBase,body:n,name:u,memoryRoleOpts:R={role:"harvester",task:"harvesting"}}=o,i=u!=null?u:`${this.role}-${Game.time}`;return Game.spawns[r].spawnCreep(n,i,{memory:R})})}run(o){if(o.store[RESOURCE_ENERGY]===0&&o.memory.task==="transferring"&&(o.memory.task="harvesting"),o.store.getFreeCapacity()===0&&o.memory.task==="harvesting"&&(o.memory.task="transferring"),o.memory.task==="harvesting"){this.harvestTask(o);return}this.roleTask(o)}roleTask(o){const r=o.room.find(FIND_MY_STRUCTURES,{filter:n=>v.includes(n.structureType)&&"store"in n&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((n,u)=>{const R=v.indexOf(n.structureType),i=v.indexOf(u.structureType);return R-i});if(r.length>0)switch(o.transfer(r[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{o.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:f(10,()=>o.say(l.transferring),{time:o.ticksToLive})}}harvestTask(o){const r=o.pos.findClosestByRange(FIND_MY_CREEPS,{filter:n=>n.memory.role==="minerStore"&&n.store[RESOURCE_ENERGY]>0});r&&o.transfer(r,RESOURCE_ENERGY)===OK&&f(10,()=>o.say(l.transferring),{time:o.ticksToLive}),this.getEnergyFromStore(o,["minerStore","source"])}};O(N,"role","harvester");let M=N;const Z=new M,I={harvester:Z};class ee{constructor(t){O(this,"tower");this.tower=t}run(){this.tower.store[RESOURCE_ENERGY]!==0&&(this.healFriendlyCreeps(),this.attackHostileCreeps(),this.repairStructures())}attackHostileCreeps(){const t=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:o=>o.body.some(r=>r.type===ATTACK||r.type===RANGED_ATTACK)});if(t.length>0){const o=t.sort((r,n)=>r.hits-r.hitsMax)[0];this.tower.attack(o)}}healFriendlyCreeps(){const t=this.tower.room.find(FIND_MY_CREEPS,{filter:o=>o.hits<o.hitsMax});if(t.length>0){const o=t.sort((r,n)=>r.hits-n.hits)[0];this.tower.heal(o)}}repairStructures(){const t=this.tower.room.find(FIND_STRUCTURES,{filter:o=>o.hits<1e4&&o.hits<o.hitsMax});if(t.length>0){const o=t.sort((r,n)=>r.hits-n.hits)[0];this.tower.repair(o)}}}const te=e=>{e.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType===STRUCTURE_TOWER}).forEach(o=>{new ee(o).run()})},re=()=>{oe(),ne()},oe=()=>{for(let e in Memory.creeps)Game.creeps[e]||(delete Memory.creeps[e],console.log("Clearing non-existing creep memory:",e))},ne=()=>{se()};FIND_DROPPED_RESOURCES,FIND_SOURCES,FIND_TOMBSTONES,FIND_RUINS;const se=()=>{var n,u;const e=Game.rooms[_.MainRoom];if(!e)return;if(!((n=Memory.sources)!=null&&n.Source)){const R=e.find(FIND_SOURCES);C.set(Memory,"sources.Source",R.map(i=>i.id))}if(!((u=Memory.sources)!=null&&u.Mineral)){const R=e.find(FIND_MINERALS);C.set(Memory,"sources.Mineral",R.map(i=>i.id))}const t=e.find(FIND_DROPPED_RESOURCES);C.set(Memory,"sources.Resource",t.map(R=>R.id));const o=e.find(FIND_RUINS);C.set(Memory,"sources.Ruin",o.map(R=>R.id));const r=e.find(FIND_TOMBSTONES);C.set(Memory,"sources.Tombstone",r.map(R=>R.id))},ie=()=>{f(10,ae)},ae=()=>{const e=Object.values(Game.spawns);for(const t of e)t.spawning?t.room.visual.text(`Spawning:${t.spawning.name}`,{...t.pos,y:t.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):t.room.visual.text(`${t.store[RESOURCE_ENERGY]}`,{...t.pos,y:t.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},Re={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]}}},ue={roleMonitor:{harvester:{count:5,body:E([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:E([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:E([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])}}},me={roleMonitor:{harvester:{count:6,body:E([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:E([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:E([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:E([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},le={roleMonitor:{harvester:{count:8,body:E([{body:CARRY,count:10},{body:MOVE,count:6}])},builder:{count:2,body:E([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:10,body:E([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:E([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},fe=[ue,me,le],ye=e=>{for(let t=e;t>=1;t--){const o=fe[t-1];if(o)return o}return Re},Ee=()=>{ce()},ce=()=>{var r,n,u,R;const e=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let i in Game.creeps){const y=Game.creeps[i];y.memory.role&&(e.set(y.memory.role,((r=e.get(y.memory.role))!=null?r:0)+1),f(10,()=>S.getVisualStatus(y)))}const t=ye((u=(n=Game.rooms[_.MainRoom].controller)==null?void 0:n.level)!=null?u:0),o=e.entries();for(let[i,y]of o)if(t.roleMonitor[i]&&y<t.roleMonitor[i].count){f(10,()=>{var c;const a=((c=t.roleMonitor[i])==null?void 0:c.body)||[],s={};for(const m of a)s[m]=(s[m]||0)+1;console.log(`${i} 数量不足`,JSON.stringify(s))}),i==="harvester"?(R=I.harvester)==null||R.create({body:t.roleMonitor[i].body,memoryRoleOpts:{role:i,task:"harvesting"}}):utils.role[i].create(d.MainBase,{body:t.roleMonitor[i].body});break}},ge=()=>{de()&&Se()&&Ce()&&Ee()},de=()=>{const e=[{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},{name:"MinRepairer",role:"repairer"}];for(const t of e)if(!Game.creeps[t.name])return Game.spawns[d.MainBase].spawnCreep(E([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]),t.name,{memory:{role:t.role,task:"harvesting"}}),f(10,()=>console.log(`MinCreepGroup中缺少: ${t.name}, 等待孵化...`)),!1;return!0},Se=()=>{const e=[{name:"FixedMiner1",pos:{x:9,y:44},targetId:"5bbcaffd9099fc012e63b77c"},{name:"FixedMiner2",pos:{x:4,y:40},targetId:"5bbcaffd9099fc012e63b77b"}];for(const t of e){const o=Game.creeps[t.name];if(o&&!o.pos.isEqualTo(t.pos.x,t.pos.y))o.moveTo(t.pos.x,t.pos.y);else if(!o)return Game.spawns[d.MainBase].spawnCreep(E([{body:WORK,count:6},{body:CARRY,count:3},{body:MOVE,count:1}]),t.name,{memory:{role:"miner",task:"harvesting",targetId:t.targetId}}),f(10,()=>console.log(`Miner中缺少: ${t.name}, 等待孵化...`)),!1}return!0},Ce=()=>{const e=[{name:"MinerStore-2",pos:{x:5,y:39}},{name:"MinerStore-3",pos:{x:10,y:43}}];for(const t of e){const o=Game.creeps[t.name];if(o&&!o.pos.isEqualTo(t.pos.x,t.pos.y))o.moveTo(t.pos.x,t.pos.y);else if(!o)return Game.spawns[d.MainBase].spawnCreep(E([{body:CARRY,count:15},{body:MOVE,count:1}]),t.name,{memory:{role:"minerStore"}}),f(10,()=>console.log(`MinerStore中缺少: ${t.name}, 等待孵化...`)),!1}return!0},Oe=()=>{ie(),re(),ge()},Te=()=>{var e,t,o,r;for(let n in Game.rooms){const u=Game.rooms[n];if((e=u.controller)!=null&&e.my){Oe();for(let R in Game.creeps){let i=Game.creeps[R];if(i.memory.role=="harvester"&&((t=I.harvester)==null||t.run(i)),i.memory.role=="builder"){const y=Object.values(Game.creeps).filter(a=>a.memory.role==="builder").findIndex((a,s)=>a.name===i.name&&s<3)!==-1;(o=U.builder)==null||o.run(i,{priority:y?"high":"low"})}i.memory.role&&((r=U[i.memory.role])==null||r.run(i))}te(u)}}};module.exports={loop:Te};global.utils={role:U};
