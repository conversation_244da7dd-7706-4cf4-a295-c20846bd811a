"use strict";var P=Object.defineProperty;var x=(s,e,t)=>e in s?P(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var f=(s,e,t)=>x(s,typeof e!="symbol"?e+"":e,t);const d=require("lodash");var y=(s=>(s.MainBase="Spawn1",s))(y||{}),g=(s=>(s.MainRoom="E49S54",s.TargetRoomFlag="TargetRoom",s))(g||{});Object.values(y);const c={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡"},l=(s,e,t={})=>{const{time:o=Game.time}=t;o%s===0&&e()},K=(s,e,t=!0)=>{const o={road:1,plain:2,swamp:10};let r=0,n=0;for(const m of s)if(m===MOVE)r++;else{if(m===CARRY&&!t)continue;n++}const i=n*o[e],a=r*2,u=a/(i||1),E=Math.ceil(1/u);return{movePerTick:Math.min(u,1),ticksPerMove:E,fatiguePerTick:i,fatigueRecover:a,moveCount:r,fatigueParts:n}};function V(s,e,t=1,o=Game.rooms[g.MainRoom]){if(!o)return[];const r={};return o.lookAtArea(e-t,s-t,e+t,s+t,!0).forEach(n=>{if(r[`${n.x},${n.y}`]!==!1){if(r[`${n.x},${n.y}`]===void 0&&(r[`${n.x},${n.y}`]=!0),n.type==="terrain"&&n.terrain==="wall"){r[`${n.x},${n.y}`]=!1;return}if(n.type==="creep"){r[`${n.x},${n.y}`]=!1;return}if(n.type==="structure"&&n.structure){!(n.structure instanceof StructureContainer)&&!(n.structure instanceof StructureRoad)&&!(n.structure instanceof StructureRampart)&&(r[`${n.x},${n.y}`]=!1);return}}}),Object.entries(r).filter(([n,i])=>i===!0).map(([n])=>{const[i,a]=n.split(",");return{x:Number(i),y:Number(a)}})}function B(s,e,t){return e==="miner"?t?s.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):e==="container"?t?s.pos.findClosestByRange(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):e==="storage"?t?s.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):e==="source"?t?s.pos.findClosestByRange(FIND_SOURCES,{filter:o=>o.energy>0}):s.room.find(FIND_SOURCES,{filter:o=>o.energy>0}):e==="mineral"?t?s.pos.findClosestByRange(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):s.room.find(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):e==="ruin"?t?s.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="tombstone"?t?s.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="resource"?t?s.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):s.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}class R{constructor(e){f(this,"role");this.role=e}getEnergyFromStore(e,t){var r,n;let o=null;if(t.length===1){const[i]=t;i&&(o=B(e,i,!0))}else{const i=[];for(const u of t){const E=(n=(r=B(e,u,!1))==null?void 0:r.filter(m=>m!==null))!=null?n:[];for(const m of E)m&&i.push(m)}let a=[];if(a=i.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),a.length>0&&(o=e.pos.findClosestByRange(a)),o||(a=i.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=e.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=e.pos.findClosestByRange(a))),!o){const u=i.filter(m=>m instanceof Creep&&m.memory&&m.memory.role==="miner"&&m.store[RESOURCE_ENERGY]>0),E=u.filter(m=>m.store.getFreeCapacity(RESOURCE_ENERGY)===0);E.length>0?o=e.pos.findClosestByRange(E):u.length>0&&(o=e.pos.findClosestByRange(u))}o||(a=i.filter(u=>e.pos.isNearTo(u.pos)?!0:u instanceof Source&&V(u.pos.x,u.pos.y,1,e.room).length>1),a.length>0&&(o=e.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof Mineral||u instanceof Deposit),a.length>0&&(o=e.pos.findClosestByRange(a)))}if(o){if(!o.pos.isNearTo(e.pos))return e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}}),o;if(o instanceof Creep&&o.memory.role==="miner")return o;if(o instanceof Source||o instanceof Mineral)return e.harvest(o),l(10,()=>e.say(c.harvesting),{time:e.ticksToLive}),o;if(o instanceof Ruin||o instanceof Tombstone)return e.withdraw(o,RESOURCE_ENERGY),l(10,()=>e.say(c.withdrawing),{time:e.ticksToLive}),o;if(o instanceof StructureStorage||o instanceof StructureContainer)return e.withdraw(o,RESOURCE_ENERGY),l(10,()=>e.say(c.withdrawing),{time:e.ticksToLive}),o;if(o instanceof Resource)return e.pickup(o),l(10,()=>e.say(c.picking),{time:e.ticksToLive}),o}return o}}f(R,"generatorRoleBody",e=>e.reduce((t,{body:o,count:r})=>t.concat(Array(r).fill(o)),[]));const M=[STRUCTURE_TOWER,STRUCTURE_RAMPART,STRUCTURE_WALL,STRUCTURE_ROAD],C=class C extends R{constructor(){super(C.role)}create(e){const{baseId:t=y.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="repairing"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_STRUCTURES,{filter:r=>!!(M.includes(r.structureType)&&r.hits<r.hitsMax||r instanceof StructureTower&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((r,n)=>{const i=M.indexOf(r.structureType),a=M.indexOf(n.structureType);return i!==a?i-a:r.hits-n.hits});if(!t.length)return;const o=t[0];if(!o.pos.isNearTo(e))e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});else if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(o,RESOURCE_ENERGY)===OK&&l(5,()=>e.say(c.transferring));else switch(e.repair(o)){case ERR_NOT_IN_RANGE:{e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};f(C,"role","repairer");let _=C;const W=new _,$=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],S=class S extends R{constructor(){super(S.role)}create(e){const{baseId:t=y.MainBase,body:o,name:r,memoryRoleOpts:n={role:"builder",task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="building"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,r)=>{const n=$.indexOf(o.structureType),i=$.indexOf(r.structureType);return n-i});if(t.length>0)switch(e.memory.task==="repairing"&&(e.memory.task="building"),e.build(t[0])){case ERR_NOT_IN_RANGE:{e.moveTo(t[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{l(10,()=>e.say(c.building),{time:e.ticksToLive});break}}else e.memory.task="repairing",this.roleTask2(e)}roleTask2(e){W.run(e)}};f(S,"role","builder");let k=S;const L=new k,N=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],T=class T extends R{constructor(){super(T.role);f(this,"create",t=>{const{baseId:o=y.MainBase,body:r,name:n,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=t,a=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(r,a,{memory:i})})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task==="transferring"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="transferring"),t.memory.task==="harvesting"?this.harvestTask(t):this.roleTask(t)}roleTask(t){const o=t.room.find(FIND_MY_STRUCTURES,{filter:r=>N.includes(r.structureType)&&"store"in r&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((r,n)=>{if(r.structureType===n.structureType)return r.pos.getRangeTo(t.pos)-n.pos.getRangeTo(t.pos);const i=N.indexOf(r.structureType),a=N.indexOf(n.structureType);return i-a});if(o.length>0)switch(t.transfer(o[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{t.moveTo(o[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:l(10,()=>t.say(c.transferring),{time:t.ticksToLive})}}harvestTask(t){const o=["resource","ruin","tombstone","container","miner"];t.body.some(r=>r.type===WORK)&&o.push("source"),this.getEnergyFromStore(t,o)}};f(T,"role","harvester");let U=T;const D=new U,O=class O extends R{constructor(){super(O.role)}create(e){const{baseId:t=y.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"moving"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.memory.task==="moving"?this.moveTask(e):this.roleTask(e)}moveTask(e){var o;let t=null;e.memory.targetId?t=Game.getObjectById(e.memory.targetId):t=(o=Memory.sources.Source.map(n=>Game.getObjectById(n)).filter(n=>n!==null).find(n=>n.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==e.name}).length===0))!=null?o:null,t&&(e.pos.isNearTo(t)?(e.memory.task="mining",e.memory.targetId=t.id):(e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}}),l(10,()=>e.say(c.moving))))}roleTask(e){const t=e.pos.findInRange(FIND_MY_CREEPS,1,{filter:o=>o.memory.role!=="miner"&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of t)e.store[RESOURCE_ENERGY]>0&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(e.transfer(o,RESOURCE_ENERGY),l(10,()=>e.say(c.transferring)));if(e.memory.targetId){const o=Game.getObjectById(e.memory.targetId);o&&(e.harvest(o),l(10,()=>e.say(c.mining)));return}e.memory.task="moving"}};f(O,"role","miner");let v=O;const j=new v,b=class b extends R{constructor(){super(b.role);f(this,"create",t=>{const{baseId:o=y.MainBase,body:r,name:n,memoryRoleOpts:i={role:"pioneer",task:"harvesting"}}=t,a=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(r,a,{memory:i})})}run(t){var o,r;t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="pioneering"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&((o=t.memory)!=null&&o.targetRoomName&&t.room.name===((r=Game.getObjectById(t.memory.targetRoomName))==null?void 0:r.id)?t.memory.task="building":t.memory.task="pioneering"),t.memory.task==="harvesting"?this.harvestingTask(t):t.memory.task==="building"?this.buildingTask(t):this.roleTask(t)}harvestingTask(t){let o=null;if(t.memory.targetRoomName&&Game.rooms[t.memory.targetRoomName])o=Game.rooms[t.memory.targetRoomName];else{const r=Game.flags[g.TargetRoomFlag];if(!r){this.getEnergyFromStore(t,["source"]);return}if(r.room)o=r.room;else{t.moveTo(r,{visualizePathStyle:{stroke:"#ffaa00"}}),console.log(`Pioneer ${t.name} 正在向目标房间旗移动`);return}}o&&this.getEnergyFromStore(t,["source"])}buildingTask(t){const o=t.pos.findInRange(FIND_STRUCTURES,1,{filter:n=>n.structureType===STRUCTURE_RAMPART}),r=t.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:n=>n.structureType===STRUCTURE_RAMPART});if(!o.length&&!r.length){t.room.createConstructionSite(t.pos,STRUCTURE_RAMPART)===OK&&console.log(`Pioneer ${t.name} 在 ${t.pos} 创建了rampart建筑工地`);return}if(r.length){const n=t.build(r[0]);n===ERR_NOT_IN_RANGE?t.moveTo(r[0]):n===OK&&console.log(`Pioneer ${t.name} 正在修建rampart`);return}if(o.length&&o[0].hits<o[0].hitsMax){const n=t.repair(o[0]);n===ERR_NOT_IN_RANGE?t.moveTo(o[0]):n===OK&&console.log(`Pioneer ${t.name} 正在修复rampart`);return}t.room.name&&(t.memory.targetRoomName=t.room.name),t.memory.task="pioneering"}roleTask(t){D.roleTask(t)}};f(b,"role","pioneer");let G=b;const z=new G,h=class h extends R{constructor(){super(h.role)}create(e){const{baseId:t=y.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="upgrading"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.controller;if(!t)return;switch(e.upgradeController(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{l(10,()=>e.say(c.upgrading),{time:e.ticksToLive});break}}}};f(h,"role","upgrader");let I=h;const H=new I,Y={harvester:D,builder:L,miner:j,upgrader:H,repairer:W,pioneer:z};class Q{constructor(e){f(this,"tower");this.tower=e}run(){this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps(),this.healFriendlyCreeps(),this.repairStructures())}attackHostileCreeps(){const e=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:t=>t.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK)});if(e.length>0){const t=e.sort((o,r)=>o.hits-o.hitsMax)[0];this.tower.attack(t)}}healFriendlyCreeps(){const e=this.tower.room.find(FIND_MY_CREEPS,{filter:t=>t.hits<t.hitsMax});if(e.length>0){const t=e.sort((o,r)=>o.hits-r.hits)[0];this.tower.heal(t)}}repairStructures(){const e=this.tower.room.find(FIND_STRUCTURES,{filter:t=>t.hits===t.hitsMax?!1:t instanceof StructureRoad||t instanceof StructureContainer||t instanceof StructureRampart||t instanceof StructureWall&&this.tower.pos.getRangeTo(t)<=6?!0:t.hits<1e5});if(e.length>0){const t=e.sort((o,r)=>o instanceof StructureRoad?-1:r instanceof StructureRoad?1:o instanceof StructureContainer?-1:r instanceof StructureContainer?1:o instanceof StructureRampart?-1:r instanceof StructureRampart?1:o.hits-r.hits)[0];this.tower.repair(t)}}}const J=s=>{s.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER}).forEach(t=>{new Q(t).run()})},X=()=>{q(),ee(),Z()},q=()=>{for(let s in Memory.creeps)Game.creeps[s]||(delete Memory.creeps[s],console.log("Clearing non-existing creep memory:",s))},Z=()=>{var e;const s={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const t of Object.values(Game.creeps))t.name.startsWith("Min")||t.memory.role&&(s[t.memory.role]=((e=s[t.memory.role])!=null?e:0)+1);Memory.creepsCount=s},ee=()=>{te()},te=(s=Game.rooms[g.MainRoom])=>{var r,n;if(!s)return;if(!((r=Memory.sources)!=null&&r.Source)){const i=s.find(FIND_SOURCES);d.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((n=Memory.sources)!=null&&n.Mineral)){const i=s.find(FIND_MINERALS);d.set(Memory,"sources.Mineral",i.map(a=>a.id))}const e=s.find(FIND_DROPPED_RESOURCES);d.set(Memory,"sources.Resource",e.map(i=>i.id));const t=s.find(FIND_RUINS);d.set(Memory,"sources.Ruin",t.map(i=>i.id));const o=s.find(FIND_TOMBSTONES);d.set(Memory,"sources.Tombstone",o.map(i=>i.id))},oe=()=>{l(10,re)},re=()=>{const s=Object.values(Game.spawns);for(const e of s)e.spawning?e.room.visual.text(`Spawning:${e.spawning.name}`,{...e.pos,y:e.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):e.room.visual.text(`${e.store[RESOURCE_ENERGY]}`,{...e.pos,y:e.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},ne={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},se={roleMonitor:{harvester:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},ie={roleMonitor:{harvester:{count:6,body:R.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},ae={roleMonitor:{harvester:{count:4,body:R.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},ue={roleMonitor:{harvester:{count:1,body:R.generatorRoleBody([{body:CARRY,count:13},{body:MOVE,count:13}])},builder:{count:1,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:MOVE,count:2}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])},repairer:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])}}},Re=[se,ie,ae,ue],me=s=>{for(let e=s;e>=1;e--){const t=Re[e-1];if(t)return t}return ne},le=()=>{fe()},fe=()=>{var o,r,n,i;const s=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.memory.role&&!u.name.startsWith("Min")&&s.set(u.memory.role,((o=s.get(u.memory.role))!=null?o:0)+1)}const e=me((n=(r=Game.rooms[g.MainRoom].controller)==null?void 0:r.level)!=null?n:0),t=s.entries();for(let[a,u]of t)if(e.roleMonitor[a]&&u<e.roleMonitor[a].count){l(10,()=>{var A,p,w;const E=((A=e.roleMonitor[a])==null?void 0:A.body)||[],m={};for(const F of E)m[F]=(m[F]||0)+1;console.log(`${a} 现有:${u} 需要:${(w=(p=e.roleMonitor[a])==null?void 0:p.count)!=null?w:0} Body:${JSON.stringify(m)}`)}),a!=="repairer"?(i=Y[a])==null||i.create({body:e.roleMonitor[a].body}):utils.role2[a].create({baseId:y.MainBase,body:e.roleMonitor[a].body});break}},ce=["MinMiner","MinMiner2"],ye=()=>{Ee()},Ee=()=>{de()&&(ge(),le())},de=()=>{const s=[...ce.map(t=>({name:t,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"}],e=Object.values(Game.creeps).filter(t=>t.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const t of s){if(t.name.startsWith("MinMiner")&&e>=2)continue;if(!Game.creeps[t.name]){let r=[];switch(t.role){case"miner":{r=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}default:{r=R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}switch(Game.spawns[y.MainBase].spawnCreep(r,t.name,{memory:{role:t.role,task:t.role==="miner"?"moving":"harvesting"}})){case OK:{console.log(`MiniGroup 孵化${t.name}成功`);break}case ERR_NOT_ENOUGH_ENERGY:{l(5,()=>console.log(`MiniGroup 缺少${t.name}, 能量不足, 等待孵化`));break}case ERR_NOT_ENOUGH_RESOURCES:{l(5,()=>console.log(`MiniGroup 缺少${t.name}, 资源不足, 等待孵化`));break}}return!1}}return!0},ge=()=>{if(!Memory.creepsCount.miner)return;const s=Memory.creepsCount.miner,e=Object.values(Game.creeps).filter(o=>o.name.startsWith("MinMiner")),t=e.slice(0,2-s);for(const o of e)t.includes(o)||(o.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${o.name}`))},Ce=()=>{oe(),X(),ye()},Se=()=>{var s,e;for(let t in Game.rooms){const o=Game.rooms[t];if((s=o.controller)!=null&&s.my){Ce();for(let r in Game.creeps){let n=Game.creeps[r];n.memory.role&&((e=Y[n.memory.role])==null||e.run(n))}J(o)}}};module.exports={loop:Se};global.utils={role2:Y,ticksPerMove:K};
