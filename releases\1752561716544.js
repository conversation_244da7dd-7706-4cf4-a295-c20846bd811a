"use strict";var H=Object.defineProperty;var z=(n,e,o)=>e in n?H(n,e,{enumerable:!0,configurable:!0,writable:!0,value:o}):n[e]=o;var y=(n,e,o)=>z(n,typeof e!="symbol"?e+"":e,o);const S=require("lodash");var g=(n=>(n.MainBase="Spawn1",n))(g||{}),l=(n=>(n.MainRoom="E49S54",n.MainRoom2="E43S52",n.TargetRoomFlag="E48S54",n.TargetRoomFlag2="E48S55",n.TargetRoomFlag3="E43S52",n.TargetRoomFlag4="E49S53",n))(l||{}),C=(n=>(n.SourceLink="687081c9e1144e042a6522ed",n.ControllerLink="68708040889973c35ae5588f",n))(C||{});Object.values(g);const Q=()=>(J(),!0),P=["5bbcaf8d9099fc012e63ac07","5bbcaf8d9099fc012e63ac08"],X=[WORK,WORK,CARRY,MOVE],J=()=>{const n=["Room2MinHarvester1"],e=["Room2MinHarvester2","Room2MinUpgrader","Room2MinUpgrader2","Room2MinUpgrader3","Room2MinBuilder","Room2MinBuilder2","Room2MinBuilder3"],o=[...n,...e];for(const r of o)if(!Game.creeps[r]){const i=Game.spawns.Spawn2;if(i&&i.spawning==null){let a;if(r.includes("Harvester"))a="harvester";else if(r.includes("Upgrader"))a="upgrader";else if(r.includes("Builder"))a="builder";else continue;const u=n.includes(r)?X:[WORK,WORK,WORK,CARRY,CARRY,MOVE,MOVE,MOVE],R={};a==="harvester"?R.task="harvesting":a==="upgrader"?R.task="upgrading":a==="builder"&&(R.task="building"),R.role=a,i.spawnCreep(u,r,{memory:R})===OK&&console.log(`[临时任务] 在Spawn2造出 ${r} (${a})`)}}const t=Object.values(Game.creeps).filter(r=>r.room.name===l.MainRoom2);for(const r of t)switch(r.store[RESOURCE_ENERGY]===0&&r.memory.task!=="harvesting"&&(r.memory.task="harvesting"),r.memory.task){case"harvesting":q(r);break;case"transferring":$(r);break;case"upgrading":x(r);break;case"building":V(r);break}},q=n=>{if(n.store.getFreeCapacity()===0&&n.memory.task==="harvesting")switch(n.memory.role){case"harvester":n.memory.task="transferring";break;case"upgrader":n.memory.task="upgrading";break;case"builder":n.memory.task="building";break}if(n.memory.task==="harvesting"){let e=Game.getObjectById(P[0]);n.memory.role==="upgrader"&&(e=Game.getObjectById(P[1])),e&&n.harvest(e)===ERR_NOT_IN_RANGE&&n.moveTo(e)}else switch(n.memory.task){case"transferring":$(n);break;case"building":V(n);break;case"upgrading":x(n);break}},$=n=>{if(n.memory.targetId){const e=Game.getObjectById(n.memory.targetId);if(e){const o=n.transfer(e,RESOURCE_ENERGY);o===ERR_NOT_IN_RANGE?n.moveTo(e):o===ERR_FULL&&(n.memory.targetId=void 0)}else n.memory.targetId=void 0}else{const e=n.room.find(FIND_MY_STRUCTURES,{filter:o=>(o.structureType===STRUCTURE_EXTENSION||o.structureType===STRUCTURE_SPAWN)&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});e.length>0&&(n.memory.targetId=e[0].id)}},x=n=>{const e=n.room.controller;e&&n.upgradeController(e)===ERR_NOT_IN_RANGE&&n.moveTo(e)},V=n=>{const e=n.pos.findClosestByPath(FIND_CONSTRUCTION_SITES);e&&n.build(e)===ERR_NOT_IN_RANGE&&n.moveTo(e)},Z=[C.SourceLink];class ee{constructor(e){y(this,"link");this.link=e}run(){if(this.canSend(1)&&Z.includes(this.link.id)){const e=Game.getObjectById(C.ControllerLink);e&&e.store.getFreeCapacity(RESOURCE_ENERGY)>0&&this.send(e)}}canSend(e=200){return this.link.cooldown===0&&this.link.store[RESOURCE_ENERGY]>=e}send(e,o){if(this.link.cooldown>0)return ERR_TIRED;const t=o?Math.min(o,this.link.store[RESOURCE_ENERGY]):this.link.store[RESOURCE_ENERGY];return this.link.transferEnergy(e,t)}get energy(){return this.link.store[RESOURCE_ENERGY]}get energyCapacity(){return this.link.store.getCapacity(RESOURCE_ENERGY)||800}get isFull(){return this.energy>=this.energyCapacity}get isEmpty(){return this.energy===0}}const oe=n=>{n.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType===STRUCTURE_LINK}).forEach(o=>{new ee(o).run()})},d={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡"},c=(n,e,o={})=>{const{time:t=Game.time}=o;t%n===0&&e()},te=(n,e,o=!0)=>{const t={road:1,plain:2,swamp:10};let r=0,s=0;for(const E of n)if(E===MOVE)r++;else{if(E===CARRY&&!o)continue;s++}const i=s*t[e],a=r*2,u=a/(i||1),R=Math.ceil(1/u);return{movePerTick:Math.min(u,1),ticksPerMove:R,fatiguePerTick:i,fatigueRecover:a,moveCount:r,fatigueParts:s}};function re(n,e,o=1,t=Game.rooms[l.MainRoom]){if(!t)return[];const r={};return t.lookAtArea(e-o,n-o,e+o,n+o,!0).forEach(s=>{if(r[`${s.x},${s.y}`]!==!1){if(r[`${s.x},${s.y}`]===void 0&&(r[`${s.x},${s.y}`]=!0),s.type==="terrain"&&s.terrain==="wall"){r[`${s.x},${s.y}`]=!1;return}if(s.type==="creep"){r[`${s.x},${s.y}`]=!1;return}if(s.type==="structure"&&s.structure){!(s.structure instanceof StructureContainer)&&!(s.structure instanceof StructureRoad)&&!(s.structure instanceof StructureRampart)&&(r[`${s.x},${s.y}`]=!1);return}}}),Object.entries(r).filter(([s,i])=>i===!0).map(([s])=>{const[i,a]=s.split(",");return{x:Number(i),y:Number(a)}})}function N(n,e,o){return e==="miner"?o?n.pos.findClosestByRange(FIND_MY_CREEPS,{filter:t=>t.memory.role==="miner"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_CREEPS,{filter:t=>t.memory.role==="miner"&&t.store[RESOURCE_ENERGY]>0}):e==="link"?o?n.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="link"&&t.store[RESOURCE_ENERGY]>0&&t.id!==C.SourceLink}):n.room.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="link"&&t.store[RESOURCE_ENERGY]>0&&t.id!==C.SourceLink}):e==="container"?o?n.pos.findClosestByRange(FIND_STRUCTURES,{filter:t=>t.structureType==="container"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_STRUCTURES,{filter:t=>t.structureType==="container"&&t.store[RESOURCE_ENERGY]>0}):e==="storage"?o?n.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="storage"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="storage"&&t.store[RESOURCE_ENERGY]>0}):e==="source"?o?n.pos.findClosestByRange(FIND_SOURCES,{filter:t=>t.energy>0}):n.room.find(FIND_SOURCES,{filter:t=>t.energy>0}):e==="mineral"?o?n.pos.findClosestByRange(FIND_MINERALS,{filter:t=>t.mineralAmount>0}):n.room.find(FIND_MINERALS,{filter:t=>t.mineralAmount>0}):e==="ruin"?o?n.pos.findClosestByRange(FIND_RUINS,{filter:t=>t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_RUINS,{filter:t=>t.store[RESOURCE_ENERGY]>0}):e==="tombstone"?o?n.pos.findClosestByRange(FIND_TOMBSTONES,{filter:t=>t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_TOMBSTONES,{filter:t=>t.store[RESOURCE_ENERGY]>0}):e==="resource"?o?n.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:t=>t.amount>0}):n.room.find(FIND_DROPPED_RESOURCES,{filter:t=>t.amount>0}):null}class m{constructor(e){y(this,"role");this.role=e}getEnergyFromStore(e,o,t=null){var s,i;let r=t;if(!r)if(o.length===1){const[a]=o;a&&(r=N(e,a,!0))}else{const a=[];for(const R of o){const E=(i=(s=N(e,R,!1))==null?void 0:s.filter(f=>f!==null))!=null?i:[];for(const f of E)f&&a.push(f)}let u=[];if(u=a.filter(R=>R instanceof Resource&&R.resourceType===RESOURCE_ENERGY||R instanceof Ruin||R instanceof Tombstone),u.length>0&&(r=e.pos.findClosestByRange(u)),r||(u=a.filter(R=>R instanceof StructureLink&&R.store[RESOURCE_ENERGY]>0&&R.id===C.ControllerLink),u.length>0&&(r=e.pos.findClosestByRange(u))),r||(u=a.filter(R=>R instanceof StructureStorage&&R.store[RESOURCE_ENERGY]>0),u.length>0&&(r=e.pos.findClosestByRange(u))),r||(u=a.filter(R=>R instanceof StructureContainer&&R.store[RESOURCE_ENERGY]>0),u.length>0&&(r=e.pos.findClosestByRange(u))),!r){const R=a.filter(f=>f instanceof Creep&&f.memory&&f.memory.role==="miner"&&f.store[RESOURCE_ENERGY]>0),E=R.filter(f=>f.store.getFreeCapacity(RESOURCE_ENERGY)===0);E.length>0?r=e.pos.findClosestByRange(E):R.length>0&&(r=e.pos.findClosestByRange(R))}r||(u=a.filter(R=>e.pos.isNearTo(R.pos)?!0:R instanceof Source&&re(R.pos.x,R.pos.y,1,e.room).length),u.length>0&&(r=e.pos.findClosestByRange(u))),r||(u=a.filter(R=>R instanceof Mineral||R instanceof Deposit),u.length>0&&(r=e.pos.findClosestByRange(u)))}if(r){if(!r.pos.isNearTo(e.pos))return e.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}}),r;if(r instanceof Creep&&r.memory.role==="miner")return r;if(r instanceof Source||r instanceof Mineral)return e.harvest(r),c(10,()=>e.say(d.harvesting),{time:e.ticksToLive}),r;if(r instanceof Ruin||r instanceof Tombstone)return e.withdraw(r,RESOURCE_ENERGY),c(10,()=>e.say(d.withdrawing),{time:e.ticksToLive}),r;if(r instanceof StructureStorage||r instanceof StructureContainer||r instanceof StructureLink)return e.withdraw(r,RESOURCE_ENERGY),c(10,()=>e.say(d.withdrawing),{time:e.ticksToLive}),r;if(r instanceof Resource)return e.pickup(r),c(10,()=>e.say(d.picking),{time:e.ticksToLive}),r}return r}getAllAvailableStores(e,o){const t=[];for(const r of o){const s=N(e,r,!1);for(const i of s)i&&t.push(i)}return t}}y(m,"generatorRoleBody",e=>e.reduce((o,{body:t,count:r})=>o.concat(Array(r).fill(t)),[]));const _=[STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_ROAD],b=class b extends m{constructor(){super(b.role)}create(e){const{baseId:o=g.MainBase,body:t,name:r,memoryRoleOpts:s={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="repairing"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const o=e.room.find(FIND_STRUCTURES,{filter:r=>!!(_.includes(r.structureType)&&r.hits<r.hitsMax||r instanceof StructureTower&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((r,s)=>{const i=_.indexOf(r.structureType),a=_.indexOf(s.structureType);return i!==a?i-a:r.hits-s.hits});if(!o.length)return;const t=o[0];if(!t.pos.isNearTo(e))e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});else if(t.structureType===STRUCTURE_TOWER&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(t,RESOURCE_ENERGY)===OK&&c(5,()=>e.say(d.transferring));else switch(e.repair(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};y(b,"role","repairer");let U=b;const L=new U,K=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],T=class T extends m{constructor(){super(T.role)}create(e){const{baseId:o=g.MainBase,body:t,name:r,memoryRoleOpts:s={role:"builder",task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="building"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const o=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((t,r)=>{const s=K.indexOf(t.structureType),i=K.indexOf(r.structureType);return s-i});if(o.length>0)switch(e.memory.task==="repairing"&&(e.memory.task="building"),e.build(o[0])){case ERR_NOT_IN_RANGE:{e.moveTo(o[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{c(10,()=>e.say(d.building),{time:e.ticksToLive});break}}else e.memory.task="repairing",this.roleTask2(e)}roleTask2(e){L.run(e)}};y(T,"role","builder");let G=T;const ne=new G,v=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],O=class O extends m{constructor(){super(O.role);y(this,"create",o=>{const{baseId:t=g.MainBase,body:r,name:s,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=o,a=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(r,a,{memory:i})})}run(o){o.store[RESOURCE_ENERGY]===0&&o.memory.task==="transferring"&&(o.memory.task="harvesting"),o.store.getFreeCapacity()===0&&o.memory.task==="harvesting"&&(o.memory.task="transferring"),o.memory.task==="harvesting"?this.harvestTask(o):this.roleTask(o)}roleTask(o,t=o.room){const r=t.find(FIND_MY_STRUCTURES,{filter:s=>v.includes(s.structureType)&&"store"in s&&s.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((s,i)=>{if(s.structureType===i.structureType)return s.pos.getRangeTo(o.pos)-i.pos.getRangeTo(o.pos);const a=v.indexOf(s.structureType),u=v.indexOf(i.structureType);return a-u});if(r.length>0){const s=[];for(const[i,a]of Object.entries(o.store))a>0&&s.push(i);for(const i of s)switch(o.transfer(r[0],i)){case ERR_NOT_IN_RANGE:{o.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});break}}}}harvestTask(o){const t=["resource","ruin","tombstone","container","miner"];o.body.some(r=>r.type===WORK)&&t.push("source"),this.getEnergyFromStore(o,t)}};y(O,"role","harvester");let p=O;const j=new p,h=class h extends m{constructor(){super(h.role)}create(e){const{baseId:o=g.MainBase,body:t,name:r,memoryRoleOpts:s={role:this.role,task:"moving"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(t,i,{memory:s})}run(e){e.memory.task==="moving"?this.moveTask(e):this.roleTask(e)}moveTask(e){var t;let o=null;e.memory.targetId?o=Game.getObjectById(e.memory.targetId):o=(t=Memory.sources.Source.map(s=>Game.getObjectById(s)).filter(s=>s!==null).find(s=>s.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==e.name}).length===0))!=null?t:null,o&&(e.pos.isNearTo(o)?(e.memory.task="mining",e.memory.targetId=o.id):(e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}}),c(10,()=>e.say(d.moving))))}roleTask(e){if(e.memory.targetId){const t=Game.getObjectById(e.memory.targetId);t?(e.harvest(t),c(10,()=>e.say(d.mining))):e.memory.task="moving"}if(e.store[RESOURCE_ENERGY]!==0){const t=Game.getObjectById(C.SourceLink);t!=null&&t.pos.isNearTo(e)&&(e.transfer(t,RESOURCE_ENERGY),c(10,()=>e.say(d.transferring)))}const o=e.pos.findInRange(FIND_MY_CREEPS,1,{filter:t=>t.memory.role!=="miner"&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const t of o)e.store[RESOURCE_ENERGY]>0&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(e.transfer(t,RESOURCE_ENERGY),c(10,()=>e.say(d.transferring)))}};y(h,"role","miner");let I=h;const se=new I,M=class M extends m{constructor(){super(M.role);y(this,"create",o=>{var u;const{baseId:t=g.MainBase,body:r,name:s,memoryRoleOpts:i}=o,a=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(r,a,{memory:{role:"pioneer",task:"harvesting",targetRoomName:(u=i==null?void 0:i.targetRoomName)!=null?u:l.TargetRoomFlag,...i}})})}run(o){o.store[RESOURCE_ENERGY]===0&&o.memory.task!=="harvesting"&&(o.memory.task="harvesting"),o.store.getFreeCapacity()===0&&o.memory.task==="harvesting"&&(o.memory.task="pioneering"),o.memory.task==="harvesting"&&this.harvestingTask(o),this.buildingTask(o),o.memory.task==="pioneering"&&this.roleTask(o)}harvestingTask(o){var r,s;let t=null;if(o.memory.targetRoomName&&Game.rooms[o.memory.targetRoomName])t=Game.rooms[o.memory.targetRoomName];else{const i=Game.flags[(r=o.memory.targetRoomName)!=null?r:l.TargetRoomFlag];if(!i){this.getEnergyFromStore(o,["source"]);return}if(i.room)t=i.room;else{o.moveTo(i,{visualizePathStyle:{stroke:"#ffaa00"}}),c(10,()=>o.say(d.moving),{time:o.ticksToLive});return}}if(t){if(o.room.name===t.name)if(o.ticksToLive&&o.ticksToLive%10===0||!o.memory.cacheTargetStoreId){const i=this.getEnergyFromStore(o,["resource","ruin","tombstone","source"]);o.memory.cacheTargetStoreId=i==null?void 0:i.id}else{const i=(s=Game.getObjectById(o.memory.cacheTargetStoreId))!=null?s:null;this.getEnergyFromStore(o,["resource","ruin","tombstone","source"],i)}else o.moveTo(t.find(FIND_SOURCES)[0],{visualizePathStyle:{stroke:"#ffaa00"}}),c(10,()=>o.say(d.moving),{time:o.ticksToLive});o.memory.targetRoomName=t.name}}buildingTask(o){const t=o.pos.findInRange(FIND_STRUCTURES,1,{filter:s=>s.structureType===STRUCTURE_ROAD&&s.hits<s.hitsMax}),r=o.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:s=>s.structureType===STRUCTURE_ROAD});r.length&&o.build(r[0]),t.length&&o.repair(t[0])}roleTask(o){j.roleTask(o,Game.rooms[l.MainRoom])}};y(M,"role","pioneer");let Y=M;const ie=new Y,k=class k extends m{constructor(){super(k.role)}create(e){const{baseId:o=g.MainBase,body:t,name:r,memoryRoleOpts:s={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="upgrading"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","link","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const o=e.room.controller;if(!o)return;switch(e.upgradeController(o)){case ERR_NOT_IN_RANGE:{e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{c(10,()=>e.say(d.upgrading),{time:e.ticksToLive});break}}}};y(k,"role","upgrader");let A=k;const ae=new A,w={harvester:j,builder:ne,miner:se,upgrader:ae,repairer:L,pioneer:ie};class Re{constructor(e){y(this,"tower");this.tower=e}run(){var e,o;this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps()||this.tower.store[RESOURCE_ENERGY]>((e=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?e:0)*.6&&this.healFriendlyCreeps()||this.tower.store[RESOURCE_ENERGY]>((o=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?o:0)*.6&&this.repairStructures())}attackHostileCreeps(){const e=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:o=>o.body.some(t=>t.type===ATTACK||t.type===RANGED_ATTACK||t.type===HEAL)||this.tower.pos.getRangeTo(o)<=10});if(e.length>0){const o=e.sort((t,r)=>t.hits-t.hitsMax)[0];return this.tower.attack(o),!0}return!1}healFriendlyCreeps(){const e=this.tower.room.find(FIND_MY_CREEPS,{filter:o=>o.hits<o.hitsMax});if(e.length>0){const o=e.sort((t,r)=>t.hits-r.hits)[0];return this.tower.heal(o),!0}return!1}repairStructures(){const e=this.tower.room.find(FIND_STRUCTURES,{filter:o=>o.hits===o.hitsMax?!1:o instanceof StructureRoad&&o.hits<o.hitsMax*.6||o instanceof StructureContainer&&o.hits<o.hitsMax*.6||o instanceof StructureRampart&&o.hits<o.hitsMax*.1||o instanceof StructureWall&&this.tower.pos.getRangeTo(o)<=6&&o.hits<o.hitsMax*5e-4?!0:o.hits<1e5});if(e.length>0){const o=e.sort((t,r)=>t instanceof StructureRoad?-1:r instanceof StructureRoad?1:t instanceof StructureContainer?-1:r instanceof StructureContainer?1:t instanceof StructureRampart?-1:r instanceof StructureRampart?1:t.hits-r.hits)[0];return this.tower.repair(o),!0}return!1}}const ue=n=>{n.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType===STRUCTURE_TOWER}).forEach(o=>{new Re(o).run()})},me=()=>{le(),fe(),ce()},le=()=>{for(let n in Memory.creeps)Game.creeps[n]||(delete Memory.creeps[n],console.log("Clearing non-existing creep memory:",n))},ce=()=>{var e;const n={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const o of Object.values(Game.creeps))o.room.name===l.MainRoom&&(o.name.startsWith("Min")||o.memory.role&&(n[o.memory.role]=((e=n[o.memory.role])!=null?e:0)+1));Memory.creepsCount=n},fe=()=>{ye()},ye=(n=Game.rooms[l.MainRoom])=>{var r,s;if(!n)return;if(!((r=Memory.sources)!=null&&r.Source)){const i=n.find(FIND_SOURCES);S.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((s=Memory.sources)!=null&&s.Mineral)){const i=n.find(FIND_MINERALS);S.set(Memory,"sources.Mineral",i.map(a=>a.id))}const e=n.find(FIND_DROPPED_RESOURCES);S.set(Memory,"sources.Resource",e.map(i=>i.id));const o=n.find(FIND_RUINS);S.set(Memory,"sources.Ruin",o.map(i=>i.id));const t=n.find(FIND_TOMBSTONES);S.set(Memory,"sources.Tombstone",t.map(i=>i.id))},de=()=>{c(10,Ee)},Ee=()=>{const n=Object.values(Game.spawns);for(const e of n)e!=null&&e.spawning?e.room.visual.text(`Spawning:${e.spawning.name}`,{...e.pos,y:e.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):e.room.visual.text(`${e.store[RESOURCE_ENERGY]}`,{...e.pos,y:e.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},ge={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},Ce={roleMonitor:{harvester:{count:5,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},Se={roleMonitor:{harvester:{count:6,body:m.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},be={roleMonitor:{harvester:{count:4,body:m.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},Te={roleMonitor:{harvester:{count:1,body:m.generatorRoleBody([{body:CARRY,count:17},{body:MOVE,count:9}])},pioneer:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:4,body:m.generatorRoleBody([{body:WORK,count:4},{body:CARRY,count:12},{body:MOVE,count:4}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},Oe={roleMonitor:{harvester:{count:1,body:m.generatorRoleBody([{body:CARRY,count:23},{body:MOVE,count:12}])},pioneer:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:4,body:m.generatorRoleBody([{body:WORK,count:10},{body:CARRY,count:7},{body:MOVE,count:9}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},he=[Ce,Se,be,Te,Oe],Me=n=>{for(let e=n;e>=1;e--){const o=he[e-1];if(o)return o}return ge},ke=()=>{Ne()},Ne=()=>{var t,r,s,i;const n=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.room.name===l.MainRoom&&u.memory.role&&!u.name.startsWith("Min")&&n.set(u.memory.role,((t=n.get(u.memory.role))!=null?t:0)+1)}const e=Me((s=(r=Game.rooms[l.MainRoom].controller)==null?void 0:r.level)!=null?s:0),o=n.entries();for(let[a,u]of o)if(e.roleMonitor[a]&&u<e.roleMonitor[a].count){c(10,()=>{var f,B,F;const R=((f=e.roleMonitor[a])==null?void 0:f.body)||[],E={};for(const W of R)E[W]=(E[W]||0)+1;console.log(`${a} 现有:${u} 需要:${(F=(B=e.roleMonitor[a])==null?void 0:B.count)!=null?F:0} Body:${JSON.stringify(E)}`)}),(i=w[a])==null||i.create({body:e.roleMonitor[a].body});break}},_e=["MinMiner","MinMiner2"],D=["MinPioneer","MinPioneer2","MinPioneer3"],ve=["MinPioneer4","MinPioneer5","MinPioneer6","MinPioneer7"],Ue=["MinPioneer10","MinPioneer11","MinPioneer12","MinPioneer13"],Ge=()=>{pe(),Ae()},pe=()=>{Ie()&&(Ye(),ke())},Ie=()=>{var o;const n=[..._e.map(t=>({name:t,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},...D.map(t=>({name:t,role:"pioneer",memoryRoleOpts:{targetRoomName:l.TargetRoomFlag}})),...ve.map(t=>({name:t,role:"pioneer",memoryRoleOpts:{targetRoomName:l.TargetRoomFlag2}})),...Ue.map(t=>({name:t,role:"pioneer",body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:4}]),memoryRoleOpts:{targetRoomName:l.TargetRoomFlag4}}))],e=Object.values(Game.creeps).filter(t=>t.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const t of n){if(t.name.startsWith("MinMiner")&&e>=2||t.name.startsWith("MinHarvester")&&Memory.creepsCount.harvester>0||t.name.startsWith("MinUpgrader")&&Memory.creepsCount.upgrader>0||t.name.startsWith("MinBuilder")&&Memory.creepsCount.builder>0||t.name.startsWith("MinRepairer")&&Memory.creepsCount.repairer>0)continue;if(!Game.creeps[t.name]){let s=[];switch(t.role){case"miner":{s=m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}case"pioneer":{s=D.includes(t.name)?m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:6},{body:MOVE,count:4}]):m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:7},{body:MOVE,count:5}]);break}default:{s=m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}if((o=Game.spawns[l.MainRoom2])!=null&&o.spawning)return!1;switch(utils.role2[t.role].create({body:s,name:t.name,memoryRoleOpts:t.memoryRoleOpts})){case OK:{console.log(`MiniGroup 正在孵化${t.name}`);break}}return!1}}return!0},Ye=()=>{if(!Memory.creepsCount.miner)return;const n=Memory.creepsCount.miner,e=Object.values(Game.creeps).filter(t=>t.name.startsWith("MinMiner")),o=e.slice(0,2-n);for(const t of e)o.includes(t)||(t.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${t.name}`))},Ae=()=>{if(Game.cpu.bucket>=1e4){const n=Game.cpu.generatePixel();n===OK?console.log("生成 1 pixel",n):console.log("生成 pixel 失败",n)}},we=()=>{de(),me(),Ge();const n=Game.rooms[l.MainRoom].find(FIND_HOSTILE_CREEPS);n.length>0&&console.log("有敌人",n)},Be=()=>{var n,e,o,t;for(let r in Game.rooms){const s=Game.rooms[r];if(s.name===l.MainRoom2)Q();else if((n=s.controller)!=null&&n.my){we();const i=Object.values(Game.creeps).filter(a=>a.room.name!==l.MainRoom2);for(let a of i){if(a.room.name!==l.MainRoom&&a.memory.role!=="pioneer"){(e=Game.rooms[l.MainRoom].controller)!=null&&e.pos&&a.moveTo((o=Game.rooms[l.MainRoom].controller)==null?void 0:o.pos);continue}a.memory.role&&((t=w[a.memory.role])==null||t.run(a))}ue(s),oe(s)}}};module.exports={loop:Be};global.utils={role2:w,ticksPerMove:te};
