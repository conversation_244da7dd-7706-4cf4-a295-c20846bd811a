"use strict";var $=Object.defineProperty;var W=(n,e,r)=>e in n?$(n,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):n[e]=r;var c=(n,e,r)=>W(n,typeof e!="symbol"?e+"":e,r);const d=require("lodash");var y=(n=>(n.MainBase="Spawn1",n))(y||{}),h=(n=>(n.MainRoom="E49S54",n))(h||{});Object.values(y);const f={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡"},m=(n,e,r={})=>{const{time:t=Game.time}=r;t%n===0&&e()},D=n=>{const e=n.length,r=n.filter(t=>t===MOVE).length*2;return Math.ceil(e/r)};function x(n,e,r=1){const t=Game.rooms[h.MainRoom];if(!t)return[];const o={};return t.lookAtArea(e-r,n-r,e+r,n+r,!0).forEach(s=>{if(o[`${s.x},${s.y}`]!==!1){if(o[`${s.x},${s.y}`]===void 0&&(o[`${s.x},${s.y}`]=!0),s.type==="terrain"&&s.terrain==="wall"){o[`${s.x},${s.y}`]=!1;return}if(s.type==="creep"){o[`${s.x},${s.y}`]=!1;return}if(s.type==="structure"&&s.structure){!(s.structure instanceof StructureContainer)&&!(s.structure instanceof StructureRoad)&&!(s.structure instanceof StructureRampart)&&(o[`${s.x},${s.y}`]=!1);return}}}),Object.entries(o).filter(([s,i])=>i===!0).map(([s])=>{const[i,a]=s.split(",");return{x:Number(i),y:Number(a)}})}function A(n,e,r){return e==="miner"?r?n.pos.findClosestByRange(FIND_MY_CREEPS,{filter:t=>t.memory.role==="miner"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_CREEPS,{filter:t=>t.memory.role==="miner"&&t.store[RESOURCE_ENERGY]>0}):e==="container"?r?n.pos.findClosestByRange(FIND_STRUCTURES,{filter:t=>t.structureType==="container"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_STRUCTURES,{filter:t=>t.structureType==="container"&&t.store[RESOURCE_ENERGY]>0}):e==="storage"?r?n.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="storage"&&t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType==="storage"&&t.store[RESOURCE_ENERGY]>0}):e==="source"?r?n.pos.findClosestByRange(FIND_SOURCES,{filter:t=>t.energy>0}):Memory.sources.Source.map(t=>Game.getObjectById(t)).filter(t=>t.energy>0):e==="mineral"?r?n.pos.findClosestByRange(FIND_MINERALS,{filter:t=>t.mineralAmount>0}):n.room.find(FIND_MINERALS,{filter:t=>t.mineralAmount>0}):e==="ruin"?r?n.pos.findClosestByRange(FIND_RUINS,{filter:t=>t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_RUINS,{filter:t=>t.store[RESOURCE_ENERGY]>0}):e==="tombstone"?r?n.pos.findClosestByRange(FIND_TOMBSTONES,{filter:t=>t.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_TOMBSTONES,{filter:t=>t.store[RESOURCE_ENERGY]>0}):e==="resource"?r?n.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:t=>t.amount>0}):n.room.find(FIND_DROPPED_RESOURCES,{filter:t=>t.amount>0}):null}class R{constructor(e){c(this,"role");this.role=e}getEnergyFromStore(e,r){var o,s;let t=null;if(r.length===1){const[i]=r;i&&(t=A(e,i,!0))}else{const i=[];for(const u of r){const E=(s=(o=A(e,u,!1))==null?void 0:o.filter(l=>l!==null))!=null?s:[];for(const l of E)l&&i.push(l)}let a=[];if(a=i.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),a.length>0&&(t=e.pos.findClosestByRange(a)),t||(a=i.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(t=e.pos.findClosestByRange(a))),t||(a=i.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(t=e.pos.findClosestByRange(a))),!t){const u=i.filter(l=>l instanceof Creep&&l.memory&&l.memory.role==="miner"&&l.store[RESOURCE_ENERGY]>0),E=u.filter(l=>l.store.getFreeCapacity(RESOURCE_ENERGY)===0);E.length>0?t=e.pos.findClosestByRange(E):u.length>0&&(t=e.pos.findClosestByRange(u))}t||(a=i.filter(u=>e.pos.isNearTo(u.pos)?!0:u instanceof Source&&x(u.pos.x,u.pos.y).length>1),a.length>0&&(t=e.pos.findClosestByRange(a))),t||(a=i.filter(u=>u instanceof Mineral||u instanceof Deposit),a.length>0&&(t=e.pos.findClosestByRange(a)))}if(t){if(!t.pos.isNearTo(e.pos))return e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}}),t;if(t instanceof Creep&&t.memory.role==="miner")return t;if(t instanceof Source||t instanceof Mineral)return e.harvest(t),m(10,()=>e.say(f.harvesting),{time:e.ticksToLive}),t;if(t instanceof Ruin||t instanceof Tombstone)return e.withdraw(t,RESOURCE_ENERGY),m(10,()=>e.say(f.withdrawing),{time:e.ticksToLive}),t;if(t instanceof StructureStorage||t instanceof StructureContainer)return e.withdraw(t,RESOURCE_ENERGY),m(10,()=>e.say(f.withdrawing),{time:e.ticksToLive}),t;if(t instanceof Resource)return e.pickup(t),m(10,()=>e.say(f.picking),{time:e.ticksToLive}),t}return t}}c(R,"generatorRoleBody",e=>e.reduce((r,{body:t,count:o})=>r.concat(Array(o).fill(t)),[]));const T=[STRUCTURE_TOWER,STRUCTURE_RAMPART,STRUCTURE_WALL,STRUCTURE_ROAD],g=class g extends R{constructor(){super(g.role)}create(e){const{baseId:r=y.MainBase,body:t,name:o,memoryRoleOpts:s={role:this.role,task:"harvesting"}}=e,i=o!=null?o:`${this.role}-${Game.time}`;return Game.spawns[r].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="repairing"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const r=e.room.find(FIND_STRUCTURES,{filter:o=>!!(T.includes(o.structureType)&&o.hits<o.hitsMax||o instanceof StructureTower&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((o,s)=>{const i=T.indexOf(o.structureType),a=T.indexOf(s.structureType);return i!==a?i-a:o.hits-s.hits});if(!r.length)return;const t=r[0];if(!t.pos.isNearTo(e))e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});else if(t.structureType===STRUCTURE_TOWER&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(t,RESOURCE_ENERGY)===OK&&m(5,()=>e.say(f.transferring));else switch(e.repair(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};c(g,"role","repairer");let N=g;const F=new N,B=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],C=class C extends R{constructor(){super(C.role)}create(e){const{baseId:r=y.MainBase,body:t,name:o,memoryRoleOpts:s={role:"builder",task:"harvesting"}}=e,i=o!=null?o:`${this.role}-${Game.time}`;return Game.spawns[r].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="building"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const r=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((t,o)=>{const s=B.indexOf(t.structureType),i=B.indexOf(o.structureType);return s-i});if(r.length>0)switch(e.memory.task==="repairing"&&(e.memory.task="building"),e.build(r[0])){case ERR_NOT_IN_RANGE:{e.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{m(10,()=>e.say(f.building),{time:e.ticksToLive});break}}else e.memory.task="repairing",this.roleTask2(e)}roleTask2(e){F.run(e)}};c(C,"role","builder");let _=C;const K=new _,M=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],S=class S extends R{constructor(){super(S.role);c(this,"create",r=>{const{baseId:t=y.MainBase,body:o,name:s,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=r,a=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,a,{memory:i})})}run(r){r.store[RESOURCE_ENERGY]===0&&r.memory.task==="transferring"&&(r.memory.task="harvesting"),r.store.getFreeCapacity()===0&&r.memory.task==="harvesting"&&(r.memory.task="transferring"),r.memory.task==="harvesting"?this.harvestTask(r):this.roleTask(r)}roleTask(r){const t=r.room.find(FIND_MY_STRUCTURES,{filter:o=>M.includes(o.structureType)&&"store"in o&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((o,s)=>{if(o.structureType===s.structureType)return o.pos.getRangeTo(r.pos)-s.pos.getRangeTo(r.pos);const i=M.indexOf(o.structureType),a=M.indexOf(s.structureType);return i-a});if(t.length>0)switch(r.transfer(t[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{r.moveTo(t[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:m(10,()=>r.say(f.transferring),{time:r.ticksToLive})}}harvestTask(r){const t=["resource","ruin","tombstone","container","miner"];r.body.some(o=>o.type===WORK)&&t.push("source"),this.getEnergyFromStore(r,t)}};c(S,"role","harvester");let U=S;const P=new U,O=class O extends R{constructor(){super(O.role)}create(e){const{baseId:r=y.MainBase,body:t,name:o,memoryRoleOpts:s={role:this.role,task:"moving"}}=e,i=o!=null?o:`${this.role}-${Game.time}`;return Game.spawns[r].spawnCreep(t,i,{memory:s})}run(e){e.memory.task==="moving"?this.moveTask(e):this.roleTask(e)}moveTask(e){var t;let r=null;e.memory.targetId?r=Game.getObjectById(e.memory.targetId):r=(t=Memory.sources.Source.map(s=>Game.getObjectById(s)).filter(s=>s!==null).find(s=>s.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==e.name}).length===0))!=null?t:null,r&&(e.pos.isNearTo(r)?(e.memory.task="mining",e.memory.targetId=r.id):(e.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}}),m(10,()=>e.say(f.moving))))}roleTask(e){const r=e.pos.findInRange(FIND_MY_CREEPS,1,{filter:t=>t.memory.role!=="miner"&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const t of r)e.store[RESOURCE_ENERGY]>0&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(e.transfer(t,RESOURCE_ENERGY),m(10,()=>e.say(f.transferring)));if(e.memory.targetId){const t=Game.getObjectById(e.memory.targetId);t&&(e.harvest(t),m(10,()=>e.say(f.mining)));return}e.memory.task="moving"}};c(O,"role","miner");let k=O;const V=new k,b=class b extends R{constructor(){super(b.role)}create(e){const{baseId:r=y.MainBase,body:t,name:o,memoryRoleOpts:s={role:this.role,task:"harvesting"}}=e,i=o!=null?o:`${this.role}-${Game.time}`;return Game.spawns[r].spawnCreep(t,i,{memory:s})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="upgrading"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(e)}roleTask(e){const r=e.room.controller;if(!r)return;switch(e.upgradeController(r)){case ERR_NOT_IN_RANGE:{e.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{m(10,()=>e.say(f.upgrading),{time:e.ticksToLive});break}}}};c(b,"role","upgrader");let v=b;const L=new v,G={harvester:P,builder:K,miner:V,upgrader:L,repairer:F};class j{constructor(e){c(this,"tower");this.tower=e}run(){this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps(),this.healFriendlyCreeps(),this.repairStructures())}attackHostileCreeps(){const e=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:r=>r.body.some(t=>t.type===ATTACK||t.type===RANGED_ATTACK)});if(e.length>0){const r=e.sort((t,o)=>t.hits-t.hitsMax)[0];this.tower.attack(r)}}healFriendlyCreeps(){const e=this.tower.room.find(FIND_MY_CREEPS,{filter:r=>r.hits<r.hitsMax});if(e.length>0){const r=e.sort((t,o)=>t.hits-o.hits)[0];this.tower.heal(r)}}repairStructures(){const e=this.tower.room.find(FIND_STRUCTURES,{filter:r=>r.hits===r.hitsMax?!1:r instanceof StructureRoad||r instanceof StructureContainer||r instanceof StructureRampart||r instanceof StructureWall&&this.tower.pos.getRangeTo(r)<=6?!0:r.hits<1e5});if(e.length>0){const r=e.sort((t,o)=>t instanceof StructureRoad?-1:o instanceof StructureRoad?1:t instanceof StructureContainer?-1:o instanceof StructureContainer?1:t instanceof StructureRampart?-1:o instanceof StructureRampart?1:t.hits-o.hits)[0];this.tower.repair(r)}}}const z=n=>{n.find(FIND_MY_STRUCTURES,{filter:r=>r.structureType===STRUCTURE_TOWER}).forEach(r=>{new j(r).run()})},H=()=>{Q(),X(),J()},Q=()=>{for(let n in Memory.creeps)Game.creeps[n]||(delete Memory.creeps[n],console.log("Clearing non-existing creep memory:",n))},J=()=>{var e;const n={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0};for(const r of Object.values(Game.creeps))r.name.startsWith("Min")||r.memory.role&&(n[r.memory.role]=((e=n[r.memory.role])!=null?e:0)+1);Memory.creepsCount=n},X=()=>{q()},q=()=>{var o,s;const n=Game.rooms[h.MainRoom];if(!n)return;if(!((o=Memory.sources)!=null&&o.Source)){const i=n.find(FIND_SOURCES);d.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((s=Memory.sources)!=null&&s.Mineral)){const i=n.find(FIND_MINERALS);d.set(Memory,"sources.Mineral",i.map(a=>a.id))}const e=n.find(FIND_DROPPED_RESOURCES);d.set(Memory,"sources.Resource",e.map(i=>i.id));const r=n.find(FIND_RUINS);d.set(Memory,"sources.Ruin",r.map(i=>i.id));const t=n.find(FIND_TOMBSTONES);d.set(Memory,"sources.Tombstone",t.map(i=>i.id))},Z=()=>{m(10,ee)},ee=()=>{const n=Object.values(Game.spawns);for(const e of n)e.spawning?e.room.visual.text(`Spawning:${e.spawning.name}`,{...e.pos,y:e.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):e.room.visual.text(`${e.store[RESOURCE_ENERGY]}`,{...e.pos,y:e.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},te={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]}}},re={roleMonitor:{harvester:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])}}},oe={roleMonitor:{harvester:{count:6,body:R.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},ne={roleMonitor:{harvester:{count:4,body:R.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},se={roleMonitor:{harvester:{count:1,body:R.generatorRoleBody([{body:CARRY,count:13},{body:MOVE,count:13}])},builder:{count:1,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:MOVE,count:2}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])},repairer:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:4},{body:MOVE,count:10}])}}},ie=[re,oe,ne,se],ae=n=>{for(let e=n;e>=1;e--){const r=ie[e-1];if(r)return r}return te},ue=()=>{Re()},Re=()=>{var t,o,s,i;const n=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.memory.role&&!u.name.startsWith("Min")&&n.set(u.memory.role,((t=n.get(u.memory.role))!=null?t:0)+1)}const e=ae((s=(o=Game.rooms[h.MainRoom].controller)==null?void 0:o.level)!=null?s:0),r=n.entries();for(let[a,u]of r)if(e.roleMonitor[a]&&u<e.roleMonitor[a].count){m(10,()=>{var p,I,Y;const E=((p=e.roleMonitor[a])==null?void 0:p.body)||[],l={};for(const w of E)l[w]=(l[w]||0)+1;console.log(`${a} 现有:${u} 需要:${(Y=(I=e.roleMonitor[a])==null?void 0:I.count)!=null?Y:0} Body:${JSON.stringify(l)}`)}),a!=="repairer"?(i=G[a])==null||i.create({body:e.roleMonitor[a].body}):utils.role2[a].create({baseId:y.MainBase,body:e.roleMonitor[a].body});break}},le=["MinMiner","MinMiner2"],me=()=>{fe()&&(ce(),ue())},fe=()=>{const n=[...le.map(r=>({name:r,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"}],e=Object.values(Game.creeps).filter(r=>r.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const r of n){if(r.name.startsWith("MinMiner")&&e>=2)continue;if(!Game.creeps[r.name]){let o=[];switch(r.role){case"miner":{o=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}default:{o=R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}switch(Game.spawns[y.MainBase].spawnCreep(o,r.name,{memory:{role:r.role,task:r.role==="miner"?"moving":"harvesting"}})){case OK:{console.log(`MiniGroup 孵化${r.name}成功`);break}case ERR_NOT_ENOUGH_ENERGY:{m(5,()=>console.log(`MiniGroup 缺少${r.name}, 能量不足, 等待孵化`));break}case ERR_NOT_ENOUGH_RESOURCES:{m(5,()=>console.log(`MiniGroup 缺少${r.name}, 资源不足, 等待孵化`));break}}return!1}}return!0},ce=()=>{if(!Memory.creepsCount.miner)return;const n=Memory.creepsCount.miner,e=Object.values(Game.creeps).filter(t=>t.name.startsWith("MinMiner")),r=e.slice(0,2-n);for(const t of e)r.includes(t)||(t.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${t.name}`))},ye=()=>{Z(),H(),me()},Ee=()=>{var n,e;for(let r in Game.rooms){const t=Game.rooms[r];if((n=t.controller)!=null&&n.my){ye();for(let o in Game.creeps){let s=Game.creeps[o];s.memory.role&&((e=G[s.memory.role])==null||e.run(s))}z(t)}}};module.exports={loop:Ee};global.utils={role2:G,ticksPerMove:D};
