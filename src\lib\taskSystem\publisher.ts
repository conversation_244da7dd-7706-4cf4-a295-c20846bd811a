import { Task, TaskQueue, TaskStatusEnum } from '../utils/taskQueue';

/**
 * 任务发布器
 * 负责根据房间状态发布各种任务
 */
export class TaskPublisher {
  private taskQueue: TaskQueue;

  constructor(taskQueue: TaskQueue) {
    this.taskQueue = taskQueue;
  }

  /**
   * 发布任务
   * @param room 房间对象
   */
  publishTasks(room: Room): void {
    this.publishHarvestTasks(room);
    this.publishUpgradeTasks(room);
    this.publishBuildTasks(room);
    this.publishRepairTasks(room);
    this.publishTransferTasks(room);
  }

  /**
   * 发布采集任务
   */
  private publishHarvestTasks(room: Room): void {
    Object.entries(Memory.rooms[room.name].sources ?? {}).forEach(([structureType, ids]) => {
      if (structureType === 'Mineral') return;
      ids.forEach((id) => {
        const target = Game.getObjectById<Resource | Ruin | Tombstone | Source | Mineral>(id);
        if (!target) return;
        const taskId = `harvest_${target.id}`;
        if (target instanceof Source && target.energy === 0) return;
        if (target instanceof Resource && target.amount === 0) return;
        if (target instanceof Ruin || target instanceof Tombstone) {
          if (target.store.energy === 0) return;
          // if (Object.values(target.store).every((v) => v === 0)) return;
        }

        let allowedCreepRoleList = ['miner', 'harvester'];
        if (!(target instanceof Source)) {
          allowedCreepRoleList = [];
        }

        if (!this.taskQueue.hasTask(taskId)) {
          const task: Task = {
            id: taskId,
            publisher: target.id,
            type: 'harvesting',
            fromId: target.id,
            toId: target.id,
            action: 'harvest',
            allowedCreepRoles: allowedCreepRoleList,
            payload: {},
            timestamp: Game.time,
            status: TaskStatusEnum.published,
            room: room.name,
            needCreepCount: 1,
          };

          this.taskQueue.add(task);
        }
      });
    });
  }

  /**
   * 发布升级任务
   */
  private publishUpgradeTasks(room: Room): void {
    if (room.controller && room.controller.my) {
      const taskId = `upgrade_${room.controller.id}`;

      if (!this.taskQueue.hasTask(taskId)) {
        const task: Task = {
          id: taskId,
          type: 'upgrading',
          publisher: room.controller.id,
          fromId: room.controller.id,
          toId: room.controller.id,
          action: 'upgradeController',
          allowedCreepRoles: ['upgrader'],
          payload: {
            progress: room.controller.progress,
            progressTotal: room.controller.progressTotal,
            level: room.controller.level,
          },
          timestamp: Game.time,
          status: TaskStatusEnum.published,
          room: room.name,
        };

        this.taskQueue.add(task);
      }
    }
  }

  /**
   * 发布建造任务
   */
  private publishBuildTasks(room: Room): void {
    const constructionSites = room.find(FIND_CONSTRUCTION_SITES);

    for (const site of constructionSites) {
      const taskId = `build_${site.id}`;

      if (!this.taskQueue.hasTask(taskId)) {
        const task: Task = {
          id: taskId,
          type: 'building',
          publisher: site.id,
          fromId: site.id,
          toId: site.id,
          action: 'build',
          allowedCreepRoles: ['builder'],
          payload: {
            structureType: site.structureType,
            progress: site.progress,
            progressTotal: site.progressTotal,
          },
          timestamp: Game.time,
          status: TaskStatusEnum.published,
          room: room.name,
        };

        this.taskQueue.add(task);
      }
    }
  }

  /**
   * 发布维修任务
   */
  private publishRepairTasks(room: Room): void {
    const structures = room.find(FIND_STRUCTURES, {
      filter: (structure) => {
        return (
          structure.hits < structure.hitsMax * 0.6 &&
          structure.structureType !== STRUCTURE_WALL &&
          structure.structureType !== STRUCTURE_RAMPART
        );
      },
    });

    for (const structure of structures) {
      const taskId = `repair_${structure.id}`;

      if (!this.taskQueue.hasTask(taskId)) {
        const task: Task = {
          id: taskId,
          type: 'repairing',
          publisher: structure.id,
          fromId: structure.id,
          toId: structure.id,
          action: 'repair',
          allowedCreepRoles: ['repairer', 'builder'],
          payload: {
            structureType: structure.structureType,
            hits: structure.hits,
            hitsMax: structure.hitsMax,
          },
          timestamp: Game.time,
          status: TaskStatusEnum.published,
          room: room.name,
        };

        this.taskQueue.add(task);
      }
    }
  }

  /**
   * 发布传输任务
   */
  private publishTransferTasks(room: Room): void {
    const allStructures = room.find(FIND_MY_STRUCTURES, {
      filter: (structure) => {
        return 'store' in structure;
      },
    });

    // 将 allStructures 分为满能量和未满能量
    for (const structure of allStructures) {
      const freeCapacity = structure.store.getFreeCapacity(RESOURCE_ENERGY);
      const taskId = `transfer_${structure.id}`;
      // 如果已满能量，已发布过任务，则需要取消
      if (freeCapacity === 0 && this.taskQueue.hasTask(taskId)) {
        this.taskQueue.remove(taskId);
      } else if (freeCapacity > 0 && !this.taskQueue.hasTask(taskId)) {
        const task: Task = {
          id: taskId,
          type: 'transferring',
          publisher: structure.id,
          fromId: '', // 从任何有能量的地方
          toId: structure.id,
          action: 'transfer',
          allowedCreepRoles: ['harvester'],
          payload: {
            resourceType: RESOURCE_ENERGY,
            amount: (structure as any).store.getFreeCapacity(RESOURCE_ENERGY),
          },
          timestamp: Game.time,
          status: TaskStatusEnum.published,
          room: room.name,
        };

        this.taskQueue.add(task);
      }
    }
  }
}
