// 以下是Memory都有简写
// creeps -> Memory.creeps
// powerCreeps -> Memory.powerCreeps
// flags -> Memory.flags
// rooms -> Memory.rooms
// spawns -> Memory.spawns

interface CreepMemory {
  role: CustomRoleType;
  currentTask?: string; // 当前执行的任务ID
  /**
   * @deprecated 仅在 tempTask 中 Min 相关用法，后续不推荐使用
   */
  task?: CustomRoleTaskType;
  /**
   * @deprecated 仅在 tempTask 中 Min 相关用法，后续不推荐使用
   */
  targetId?: string;
  // targetRoomName?: string;

  // 任务系统相关
  // cacheTargetStoreId?: string;
}

interface RoomMemory {
  // name: string;
  taskQueue: import('@/lib/utils/taskMap').Task[];
  taskQueueVersion: number;
  creepsCount?: Record<CustomRoleType, number>;
  sources?: Partial<Record<StructureConstant, string[]>>;
}

interface Memory {
  [x: string]: any;
  rooms: Record<string, RoomMemory>;
}
