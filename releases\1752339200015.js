"use strict";var K=Object.defineProperty;var V=(n,t,e)=>t in n?K(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var c=(n,t,e)=>V(n,typeof t!="symbol"?t+"":t,e);const C=require("lodash");var E=(n=>(n.MainBase="Spawn1",n))(E||{}),y=(n=>(n.MainRoom="E49S54",n.TargetRoomFlag="E48S54",n.TargetRoomFlag2="E48S55",n))(y||{}),g=(n=>(n.SourceLink="687081c9e1144e042a6522ed",n.ControllerLink="68708040889973c35ae5588f",n))(g||{});Object.values(E);const L=[g.SourceLink];class j{constructor(t){c(this,"link");this.link=t}run(){if(this.canSend(1)&&L.includes(this.link.id)){const t=Game.getObjectById(g.ControllerLink);t&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0&&this.send(t)}}canSend(t=200){return this.link.cooldown===0&&this.link.store[RESOURCE_ENERGY]>=t}send(t,e){if(this.link.cooldown>0)return ERR_TIRED;const o=e?Math.min(e,this.link.store[RESOURCE_ENERGY]):this.link.store[RESOURCE_ENERGY];return this.link.transferEnergy(t,o)}get energy(){return this.link.store[RESOURCE_ENERGY]}get energyCapacity(){return this.link.store.getCapacity(RESOURCE_ENERGY)||800}get isFull(){return this.energy>=this.energyCapacity}get isEmpty(){return this.energy===0}}const z=n=>{n.find(FIND_MY_STRUCTURES,{filter:e=>e.structureType===STRUCTURE_LINK}).forEach(e=>{new j(e).run()})},f={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡",repairing:"🔧"},l=(n,t,e={})=>{const{time:o=Game.time}=e;o%n===0&&t()},H=(n,t,e=!0)=>{const o={road:1,plain:2,swamp:10};let s=0,r=0;for(const m of n)if(m===MOVE)s++;else{if(m===CARRY&&!e)continue;r++}const i=r*o[t],a=s*2,u=a/(i||1),d=Math.ceil(1/u);return{movePerTick:Math.min(u,1),ticksPerMove:d,fatiguePerTick:i,fatigueRecover:a,moveCount:s,fatigueParts:r}};function Q(n,t,e=1,o=Game.rooms[y.MainRoom]){if(!o)return[];const s={};return o.lookAtArea(t-e,n-e,t+e,n+e,!0).forEach(r=>{if(s[`${r.x},${r.y}`]!==!1){if(s[`${r.x},${r.y}`]===void 0&&(s[`${r.x},${r.y}`]=!0),r.type==="terrain"&&r.terrain==="wall"){s[`${r.x},${r.y}`]=!1;return}if(r.type==="creep"){s[`${r.x},${r.y}`]=!1;return}if(r.type==="structure"&&r.structure){!(r.structure instanceof StructureContainer)&&!(r.structure instanceof StructureRoad)&&!(r.structure instanceof StructureRampart)&&(s[`${r.x},${r.y}`]=!1);return}}}),Object.entries(s).filter(([r,i])=>i===!0).map(([r])=>{const[i,a]=r.split(",");return{x:Number(i),y:Number(a)}})}function N(n,t,e){return t==="miner"?e?n.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):t==="link"?e?n.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="link"&&o.store[RESOURCE_ENERGY]>0&&o.id!==g.SourceLink}):n.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="link"&&o.store[RESOURCE_ENERGY]>0&&o.id!==g.SourceLink}):t==="container"?e?n.pos.findClosestByRange(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):t==="storage"?e?n.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):t==="source"?e?n.pos.findClosestByRange(FIND_SOURCES,{filter:o=>o.energy>0}):n.room.find(FIND_SOURCES,{filter:o=>o.energy>0}):t==="mineral"?e?n.pos.findClosestByRange(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):n.room.find(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):t==="ruin"?e?n.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):t==="tombstone"?e?n.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):n.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):t==="resource"?e?n.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):n.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}class R{constructor(t){c(this,"role");this.role=t}getEnergyFromStore(t,e){var s,r;let o=null;if(e.length===1){const[i]=e;i&&(o=N(t,i,!0))}else{const i=[];for(const u of e){const d=(r=(s=N(t,u,!1))==null?void 0:s.filter(m=>m!==null))!=null?r:[];for(const m of d)m&&i.push(m)}let a=[];if(a=i.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),a.length>0&&(o=t.pos.findClosestByRange(a)),o||(a=i.filter(u=>u instanceof StructureLink&&u.store[RESOURCE_ENERGY]>0&&u.id===g.ControllerLink),a.length>0&&(o=t.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=t.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=t.pos.findClosestByRange(a))),!o){const u=i.filter(m=>m instanceof Creep&&m.memory&&m.memory.role==="miner"&&m.store[RESOURCE_ENERGY]>0),d=u.filter(m=>m.store.getFreeCapacity(RESOURCE_ENERGY)===0);d.length>0?o=t.pos.findClosestByRange(d):u.length>0&&(o=t.pos.findClosestByRange(u))}o||(a=i.filter(u=>t.pos.isNearTo(u.pos)?!0:u instanceof Source&&Q(u.pos.x,u.pos.y,1,t.room).length),a.length>0&&(o=t.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof Mineral||u instanceof Deposit),a.length>0&&(o=t.pos.findClosestByRange(a)))}if(o){if(!o.pos.isNearTo(t.pos))return t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}}),o;if(o instanceof Creep&&o.memory.role==="miner")return o;if(o instanceof Source||o instanceof Mineral)return t.harvest(o),l(10,()=>t.say(f.harvesting),{time:t.ticksToLive}),o;if(o instanceof Ruin||o instanceof Tombstone)return t.withdraw(o,RESOURCE_ENERGY),l(10,()=>t.say(f.withdrawing),{time:t.ticksToLive}),o;if(o instanceof StructureStorage||o instanceof StructureContainer||o instanceof StructureLink)return t.withdraw(o,RESOURCE_ENERGY),l(10,()=>t.say(f.withdrawing),{time:t.ticksToLive}),o;if(o instanceof Resource)return t.pickup(o),l(10,()=>t.say(f.picking),{time:t.ticksToLive}),o}return o}getAllAvailableStores(t,e){const o=[];for(const s of e){const r=N(t,s,!1);for(const i of r)i&&o.push(i)}return o}}c(R,"generatorRoleBody",t=>t.reduce((e,{body:o,count:s})=>e.concat(Array(s).fill(o)),[]));const k=[STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_ROAD],S=class S extends R{constructor(){super(S.role)}create(t){const{baseId:e=E.MainBase,body:o,name:s,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=t,i=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="repairing"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.find(FIND_STRUCTURES,{filter:s=>!!(k.includes(s.structureType)&&s.hits<s.hitsMax||s instanceof StructureTower&&s.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((s,r)=>{const i=k.indexOf(s.structureType),a=k.indexOf(r.structureType);return i!==a?i-a:s.hits-r.hits});if(!e.length)return;const o=e[0];if(!o.pos.isNearTo(t))t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});else if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)t.transfer(o,RESOURCE_ENERGY)===OK&&l(5,()=>t.say(f.transferring));else switch(t.repair(o)){case ERR_NOT_IN_RANGE:{t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};c(S,"role","repairer");let U=S;const $=new U,D=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],O=class O extends R{constructor(){super(O.role)}create(t){const{baseId:e=E.MainBase,body:o,name:s,memoryRoleOpts:r={role:"builder",task:"harvesting"}}=t,i=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="building"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,s)=>{const r=D.indexOf(o.structureType),i=D.indexOf(s.structureType);return r-i});if(e.length>0)switch(t.memory.task==="repairing"&&(t.memory.task="building"),t.build(e[0])){case ERR_NOT_IN_RANGE:{t.moveTo(e[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{l(10,()=>t.say(f.building),{time:t.ticksToLive});break}}else t.memory.task="repairing",this.roleTask2(t)}roleTask2(t){$.run(t)}};c(O,"role","builder");let v=O;const J=new v,_=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],b=class b extends R{constructor(){super(b.role);c(this,"create",e=>{const{baseId:o=E.MainBase,body:s,name:r,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=e,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(s,a,{memory:i})})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task==="transferring"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="transferring"),e.memory.task==="harvesting"?this.harvestTask(e):this.roleTask(e)}roleTask(e,o=e.room){const s=o.find(FIND_MY_STRUCTURES,{filter:r=>_.includes(r.structureType)&&"store"in r&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((r,i)=>{if(r.structureType===i.structureType)return r.pos.getRangeTo(e.pos)-i.pos.getRangeTo(e.pos);const a=_.indexOf(r.structureType),u=_.indexOf(i.structureType);return a-u});if(s.length>0)switch(e.transfer(s[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{e.moveTo(s[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:l(10,()=>e.say(f.transferring),{time:e.ticksToLive})}}harvestTask(e){const o=["resource","ruin","tombstone","container","miner"];e.body.some(s=>s.type===WORK)&&o.push("source"),this.getEnergyFromStore(e,o)}};c(b,"role","harvester");let G=b;const x=new G,h=class h extends R{constructor(){super(h.role)}create(t){const{baseId:e=E.MainBase,body:o,name:s,memoryRoleOpts:r={role:this.role,task:"moving"}}=t,i=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.memory.task==="moving"?this.moveTask(t):this.roleTask(t)}moveTask(t){var o;let e=null;t.memory.targetId?e=Game.getObjectById(t.memory.targetId):e=(o=Memory.sources.Source.map(r=>Game.getObjectById(r)).filter(r=>r!==null).find(r=>r.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==t.name}).length===0))!=null?o:null,e&&(t.pos.isNearTo(e)?(t.memory.task="mining",t.memory.targetId=e.id):(t.moveTo(e,{visualizePathStyle:{stroke:"#ffffff"}}),l(10,()=>t.say(f.moving))))}roleTask(t){if(t.memory.targetId){const o=Game.getObjectById(t.memory.targetId);o?(t.harvest(o),l(10,()=>t.say(f.mining))):t.memory.task="moving"}if(t.store[RESOURCE_ENERGY]!==0){const o=Game.getObjectById(g.SourceLink);o!=null&&o.pos.isNearTo(t)&&(t.transfer(o,RESOURCE_ENERGY),l(10,()=>t.say(f.transferring)))}const e=t.pos.findInRange(FIND_MY_CREEPS,1,{filter:o=>o.memory.role!=="miner"&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of e)t.store[RESOURCE_ENERGY]>0&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(t.transfer(o,RESOURCE_ENERGY),l(10,()=>t.say(f.transferring)))}};c(h,"role","miner");let Y=h;const X=new Y,T=class T extends R{constructor(){super(T.role);c(this,"create",e=>{var u;const{baseId:o=E.MainBase,body:s,name:r,memoryRoleOpts:i}=e,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(s,a,{memory:{role:"pioneer",task:"harvesting",targetRoomName:(u=i==null?void 0:i.targetRoomName)!=null?u:y.TargetRoomFlag,...i}})})}run(e){var o;e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&(e.room.name===((o=e.memory)==null?void 0:o.targetRoomName)&&e.memory.task==="harvesting"?e.memory.task="building":e.memory.task="pioneering"),e.store[RESOURCE_ENERGY]>0&&e.memory.task!=="harvesting"&&this.buildingTask(e),e.memory.task==="harvesting"?this.harvestingTask(e):e.memory.task==="building"?this.buildingTask(e):e.memory.task==="pioneering"&&this.roleTask(e)}harvestingTask(e){var s;let o=null;if(e.memory.targetRoomName&&Game.rooms[e.memory.targetRoomName])o=Game.rooms[e.memory.targetRoomName];else{const r=Game.flags[(s=e.memory.targetRoomName)!=null?s:y.TargetRoomFlag];if(!r){this.getEnergyFromStore(e,["source"]);return}if(r.room)o=r.room;else{e.moveTo(r,{visualizePathStyle:{stroke:"#ffaa00"}}),l(10,()=>e.say(f.moving),{time:e.ticksToLive});return}}o&&(e.room.name===o.name?this.getEnergyFromStore(e,["resource","ruin","tombstone","source"]):(e.moveTo(o.find(FIND_SOURCES)[0],{visualizePathStyle:{stroke:"#ffaa00"}}),l(10,()=>e.say(f.moving),{time:e.ticksToLive})),e.memory.targetRoomName=o.name)}buildingTask(e){const o=e.pos.findInRange(FIND_STRUCTURES,1,{filter:r=>r.structureType===STRUCTURE_ROAD}),s=e.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:r=>r.structureType===STRUCTURE_ROAD});if(s.length){const r=e.build(s[0]);r===ERR_NOT_IN_RANGE?e.moveTo(s[0]):r===OK&&l(10,()=>e.say(f.building),{time:e.ticksToLive});return}if(o.length&&o[0].hits<o[0].hitsMax){const r=e.repair(o[0]);r===ERR_NOT_IN_RANGE?e.moveTo(o[0]):r===OK&&l(10,()=>e.say(f.repairing),{time:e.ticksToLive});return}e.memory.task="pioneering"}roleTask(e){x.roleTask(e,Game.rooms[y.MainRoom])}};c(T,"role","pioneer");let I=T;const q=new I,M=class M extends R{constructor(){super(M.role)}create(t){const{baseId:e=E.MainBase,body:o,name:s,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=t,i=s!=null?s:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="upgrading"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["resource","ruin","tombstone","link","storage","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.controller;if(!e)return;switch(t.upgradeController(e)){case ERR_NOT_IN_RANGE:{t.moveTo(e,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{l(10,()=>t.say(f.upgrading),{time:t.ticksToLive});break}}}};c(M,"role","upgrader");let p=M;const Z=new p,A={harvester:x,builder:J,miner:X,upgrader:Z,repairer:$,pioneer:q};class ee{constructor(t){c(this,"tower");this.tower=t}run(){var t;this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps()||this.healFriendlyCreeps()||this.tower.store[RESOURCE_ENERGY]>((t=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?t:0)*.6&&this.repairStructures())}attackHostileCreeps(){const t=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:e=>e.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK)});if(t.length>0){const e=t.sort((o,s)=>o.hits-o.hitsMax)[0];return this.tower.attack(e),!0}return!1}healFriendlyCreeps(){const t=this.tower.room.find(FIND_MY_CREEPS,{filter:e=>e.hits<e.hitsMax});if(t.length>0){const e=t.sort((o,s)=>o.hits-s.hits)[0];return this.tower.heal(e),!0}return!1}repairStructures(){const t=this.tower.room.find(FIND_STRUCTURES,{filter:e=>e.hits===e.hitsMax?!1:e instanceof StructureRoad&&e.hits<e.hitsMax*.6||e instanceof StructureContainer&&e.hits<e.hitsMax*.6||e instanceof StructureRampart&&e.hits<e.hitsMax*.1||e instanceof StructureWall&&this.tower.pos.getRangeTo(e)<=6&&e.hits<e.hitsMax*5e-4?!0:e.hits<1e5});if(t.length>0){const e=t.sort((o,s)=>o instanceof StructureRoad?-1:s instanceof StructureRoad?1:o instanceof StructureContainer?-1:s instanceof StructureContainer?1:o instanceof StructureRampart?-1:s instanceof StructureRampart?1:o.hits-s.hits)[0];return this.tower.repair(e),!0}return!1}}const te=n=>{n.find(FIND_MY_STRUCTURES,{filter:e=>e.structureType===STRUCTURE_TOWER}).forEach(e=>{new ee(e).run()})},oe=()=>{re(),se(),ne()},re=()=>{for(let n in Memory.creeps)Game.creeps[n]||(delete Memory.creeps[n],console.log("Clearing non-existing creep memory:",n))},ne=()=>{var t;const n={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const e of Object.values(Game.creeps))e.name.startsWith("Min")||e.memory.role&&(n[e.memory.role]=((t=n[e.memory.role])!=null?t:0)+1);Memory.creepsCount=n},se=()=>{ie()},ie=(n=Game.rooms[y.MainRoom])=>{var s,r;if(!n)return;if(!((s=Memory.sources)!=null&&s.Source)){const i=n.find(FIND_SOURCES);C.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((r=Memory.sources)!=null&&r.Mineral)){const i=n.find(FIND_MINERALS);C.set(Memory,"sources.Mineral",i.map(a=>a.id))}const t=n.find(FIND_DROPPED_RESOURCES);C.set(Memory,"sources.Resource",t.map(i=>i.id));const e=n.find(FIND_RUINS);C.set(Memory,"sources.Ruin",e.map(i=>i.id));const o=n.find(FIND_TOMBSTONES);C.set(Memory,"sources.Tombstone",o.map(i=>i.id))},ae=()=>{l(10,ue)},ue=()=>{const n=Object.values(Game.spawns);for(const t of n)t.spawning?t.room.visual.text(`Spawning:${t.spawning.name}`,{...t.pos,y:t.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):t.room.visual.text(`${t.store[RESOURCE_ENERGY]}`,{...t.pos,y:t.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},Re={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},le={roleMonitor:{harvester:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},me={roleMonitor:{harvester:{count:6,body:R.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},fe={roleMonitor:{harvester:{count:4,body:R.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},ce={roleMonitor:{harvester:{count:1,body:R.generatorRoleBody([{body:CARRY,count:17},{body:MOVE,count:9}])},pioneer:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:4,body:R.generatorRoleBody([{body:WORK,count:4},{body:CARRY,count:12},{body:MOVE,count:4}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},ye={roleMonitor:{harvester:{count:2,body:R.generatorRoleBody([{body:CARRY,count:23},{body:MOVE,count:12}])},pioneer:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:4,body:R.generatorRoleBody([{body:WORK,count:4},{body:CARRY,count:16},{body:MOVE,count:10}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},Ee=[le,me,fe,ce,ye],de=n=>{for(let t=n;t>=1;t--){const e=Ee[t-1];if(e)return e}return Re},ge=()=>{Ce()},Ce=()=>{var o,s,r,i;const n=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.memory.role&&!u.name.startsWith("Min")&&n.set(u.memory.role,((o=n.get(u.memory.role))!=null?o:0)+1)}const t=de((r=(s=Game.rooms[y.MainRoom].controller)==null?void 0:s.level)!=null?r:0),e=n.entries();for(let[a,u]of e)if(t.roleMonitor[a]&&u<t.roleMonitor[a].count){l(10,()=>{var w,B,F;const d=((w=t.roleMonitor[a])==null?void 0:w.body)||[],m={};for(const W of d)m[W]=(m[W]||0)+1;console.log(`${a} 现有:${u} 需要:${(F=(B=t.roleMonitor[a])==null?void 0:B.count)!=null?F:0} Body:${JSON.stringify(m)}`)}),a!=="repairer"?(i=A[a])==null||i.create({body:t.roleMonitor[a].body}):utils.role2[a].create({baseId:E.MainBase,body:t.roleMonitor[a].body});break}},Se=["MinMiner","MinMiner2"],P=["MinPioneer","MinPioneer2","MinPioneer3"],Oe=["MinPioneer4","MinPioneer5","MinPioneer6","MinPioneer7"],be=()=>{he(),Ne()},he=()=>{Te()&&(Me(),ge())},Te=()=>{const n=[...Se.map(e=>({name:e,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},...P.map(e=>({name:e,role:"pioneer",memoryRoleOpts:{targetRoomName:y.TargetRoomFlag}})),...Oe.map(e=>({name:e,role:"pioneer",memoryRoleOpts:{targetRoomName:y.TargetRoomFlag2}}))],t=Object.values(Game.creeps).filter(e=>e.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const e of n){if(e.name.startsWith("MinMiner")&&t>=2||e.name.startsWith("MinHarvester")&&Memory.creepsCount.harvester>0||e.name.startsWith("MinUpgrader")&&Memory.creepsCount.upgrader>0||e.name.startsWith("MinBuilder")&&Memory.creepsCount.builder>0||e.name.startsWith("MinRepairer")&&Memory.creepsCount.repairer>0)continue;if(!Game.creeps[e.name]){let s=[];switch(e.role){case"miner":{s=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}case"pioneer":{s=P.includes(e.name)?R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:6},{body:MOVE,count:4}]):R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:7},{body:MOVE,count:5}]);break}default:{s=R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}const r=utils.role2[e.role].create({body:s,name:e.name,memoryRoleOpts:e.memoryRoleOpts});switch(r){case OK:{console.log(`MiniGroup 孵化${e.name}成功`);break}case ERR_NOT_ENOUGH_ENERGY:{l(5,()=>console.log(`MiniGroup 缺少${e.name}, 能量不足, 等待孵化`));break}case ERR_NOT_ENOUGH_RESOURCES:{l(5,()=>console.log(`MiniGroup 缺少${e.name}, 资源不足, 等待孵化`));break}default:l(5,()=>console.log(`MiniGroup 孵化${e.name}失败`,r));break}return!1}}return!0},Me=()=>{if(!Memory.creepsCount.miner)return;const n=Memory.creepsCount.miner,t=Object.values(Game.creeps).filter(o=>o.name.startsWith("MinMiner")),e=t.slice(0,2-n);for(const o of t)e.includes(o)||(o.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${o.name}`))},Ne=()=>{if(Game.cpu.bucket>=1e4){const n=Game.cpu.generatePixel();n===OK?console.log("生成 1 pixel",n):console.log("生成 pixel 失败",n)}},ke=()=>{ae(),oe(),be();const n=Game.rooms[y.MainRoom].find(FIND_HOSTILE_CREEPS);n.length>0&&console.log("有敌人",n)},_e=()=>{var n,t;for(let e in Game.rooms){const o=Game.rooms[e];if((n=o.controller)!=null&&n.my){ke();for(let s in Game.creeps){let r=Game.creeps[s];r.memory.role&&((t=A[r.memory.role])==null||t.run(r))}te(o),z(o)}}};module.exports={loop:_e};global.utils={role2:A,ticksPerMove:H};
