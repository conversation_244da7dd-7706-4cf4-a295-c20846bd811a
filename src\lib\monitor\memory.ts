import { ROOM_ID_ENUM } from '@/constant';
import { set } from 'lodash';

export const memory = (room: Room) => {
  clearCreepMemory();
  creepsCount(room);
  resources(room);
};

// 清理Memory中不存在的creep
const clearCreepMemory = () => {
  for (let name in Memory.creeps) {
    if (!Game.creeps[name]) {
      delete Memory.creeps[name];
      console.log('Clearing non-existing creep memory:', name);
    }
  }
};

// Creeps 类型计数
const creepsCount = (room: Room) => {
  const creepTypeCount: Record<CustomRoleType, number> = {
    harvester: 0,
    builder: 0,
    upgrader: 0,
    miner: 0,
    repairer: 0,
    pioneer: 0,
    claimer: 0,
  };
  for (const creep of Object.values(Game.creeps)) {
    // 最小组不参与计数
    if (creep.name.startsWith('Min') || creep.room.name !== room.name) continue;
    if (creep.memory.role) {
      creepTypeCount[creep.memory.role] = (creepTypeCount[creep.memory.role] ?? 0) + 1;
    }
  }
  Memory.rooms[room.name].creepsCount = creepTypeCount;
};

// 资源监控
export const resources = (room: Room) => {
  getEnergySource(room);
};

// 地图资源, 获取能量源
const getEnergySource = (room = Game.rooms[ROOM_ID_ENUM.MainRoom]) => {
  if (!room) return;
  // Source和Mineral资源是恒定的, 只需要初始化
  if (!Memory.sources?.Source) {
    const sources = room.find(FIND_SOURCES);
    set(
      Memory.rooms[room.name],
      'sources.Source',
      sources.map((s) => s.id)
    );
  }
  if (!Memory.sources?.Mineral) {
    const minerals = room.find(FIND_MINERALS);
    set(
      Memory.rooms[room.name],
      'sources.Mineral',
      minerals.map((mine) => mine.id)
    );
  }

  // 掉落资源
  const resources = room.find(FIND_DROPPED_RESOURCES);
  set(
    Memory.rooms[room.name],
    'sources.Resource',
    resources.map((resource) => resource.id)
  );

  // 遗迹
  const ruins = room.find(FIND_RUINS);
  set(
    Memory.rooms[room.name],
    'sources.Ruin',
    ruins.map((ruin) => ruin.id)
  );

  // 墓碑
  const tombstones = room.find(FIND_TOMBSTONES);
  set(
    Memory.rooms[room.name],
    'sources.Tombstone',
    tombstones.map((tombstone) => tombstone.id)
  );
};
