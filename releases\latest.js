"use strict";var z=Object.defineProperty;var J=(s,e,t)=>e in s?z(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var f=(s,e,t)=>J(s,typeof e!="symbol"?e+"":e,t);const h=require("lodash");var T=(s=>(s.MainBase="Spawn1",s))(T||{}),c=(s=>(s.MainRoom="E49S54",s.MainRoom2="E43S52",s.TargetRoomFlag="E48S54",s.TargetRoomFlag2="E48S55",s.TargetRoomFlag3="E43S52",s.TargetRoomFlag4="E49S53",s))(c||{}),S=(s=>(s.SourceLink="687081c9e1144e042a6522ed",s.ControllerLink="68708040889973c35ae5588f",s))(S||{});Object.values(T);const M=s=>s.reduce((e,{body:t,count:o})=>e.concat(Array(o).fill(t)),[]),X=()=>(Z(),te(Game.rooms[c.MainRoom2]),!0),k=["5bbcaf8d9099fc012e63ac07","5bbcaf8d9099fc012e63ac08"];WORK,WORK,CARRY,MOVE;const Z=()=>{const s=["Room2MinHarvester1"],e=["Room2MinMiner","Room2MinMiner2","Room2MinUpgrader","Room2MinRepairer","Room2MinBuilder","Room2MinBuilder2","Room2MinBuilder3","Room2MinHarvester2","Room2MinUpgrader2","Room2MinUpgrader3","Room2MinUpgrader4"],t=[...s,...e];for(const r of t)if(!Game.creeps[r]){const i=Game.spawns.Spawn2;if(i&&i.spawning==null){let a;if(r.includes("Harvester"))a="harvester";else if(r.includes("Upgrader"))a="upgrader";else if(r.includes("Builder"))a="builder";else if(r.includes("Miner"))a="miner";else if(r.includes("Repairer"))a="repairer";else continue;let l=M([{body:WORK,count:4},{body:CARRY,count:6},{body:MOVE,count:5}]);r.includes("MinMiner")&&(l=M([{body:WORK,count:6},{body:MOVE,count:3}])),r.includes("MinHarvester")&&(l=M([{body:WORK,count:2},{body:CARRY,count:7},{body:MOVE,count:5}]));const R={};a==="harvester"?R.task="harvesting":a==="upgrader"?R.task="upgrading":a==="builder"?R.task="building":a==="miner"?R.task="mining":a==="repairing"&&(R.task="repairing"),R.role=a,i.spawnCreep(l,r,{memory:R})===OK&&console.log(`[临时任务] 在Spawn2造出 ${r} (${a})`)}break}const o=Object.values(Game.creeps).filter(r=>r.room.name===c.MainRoom2);for(const r of o)switch(r.store[RESOURCE_ENERGY]===0&&r.memory.task!=="harvesting"&&(r.memory.task="harvesting"),r.memory.task){case"harvesting":ee(r);break;case"transferring":K(r);break;case"upgrading":L(r);break;case"building":Q(r);break;case"mining":W(r);break;case"repairing":V(r);break}},ee=s=>{if(s.store.getFreeCapacity()===0&&s.memory.task==="harvesting")switch(s.memory.role){case"harvester":s.memory.task="transferring";break;case"upgrader":s.memory.task="upgrading";break;case"builder":s.memory.task="building";break;case"repairer":s.memory.task="repairing";break}if(s.memory.task==="harvesting"){let e=Game.getObjectById(k[0]);if(s.memory.role==="harvester"){const t=s.room.find(FIND_DROPPED_RESOURCES,{filter:r=>r.resourceType===RESOURCE_ENERGY}).sort((r,n)=>r.pos.getRangeTo(s.pos)-n.pos.getRangeTo(s.pos));if(t.length>0&&s.pickup(t[0])===ERR_NOT_IN_RANGE){s.moveTo(t[0]);return}const o=s.room.find(FIND_STRUCTURES,{filter:r=>r.structureType===STRUCTURE_CONTAINER&&r.store[RESOURCE_ENERGY]>0});if(o.length>0&&s.withdraw(o[0],RESOURCE_ENERGY)===ERR_NOT_IN_RANGE){s.moveTo(o[0]);return}}if((s.memory.role==="upgrader"||s.memory.role==="miner")&&(s.name==="Room2MinMiner2"?e=Game.getObjectById(k[0]):e=Game.getObjectById(k[1])),s.memory.role==="upgrader"){const t=s.pos.findInRange(FIND_STRUCTURES,1,{filter:r=>r.structureType===STRUCTURE_CONTAINER&&r.store[RESOURCE_ENERGY]>0});if(t.length>0){s.withdraw(t[0],RESOURCE_ENERGY)===ERR_NOT_IN_RANGE&&s.moveTo(t[0]);return}const o=s.pos.findInRange(FIND_DROPPED_RESOURCES,1,{filter:r=>r.resourceType===RESOURCE_ENERGY});if(o.length>0){s.pickup(o[0])===ERR_NOT_IN_RANGE&&s.moveTo(o[0]);return}}e&&s.harvest(e)===ERR_NOT_IN_RANGE&&s.moveTo(e)}else switch(s.memory.task){case"mining":W(s);break;case"transferring":K(s);break;case"building":Q(s);break;case"upgrading":L(s);break;case"repairing":V(s);break}},W=s=>{const e=Game.getObjectById(k[1]);e&&s.harvest(e)===ERR_NOT_IN_RANGE&&s.moveTo(e)},K=s=>{if(s.memory.targetId){const e=Game.getObjectById(s.memory.targetId);if(e){const t=s.transfer(e,RESOURCE_ENERGY);t===ERR_NOT_IN_RANGE?s.moveTo(e):t===ERR_FULL&&(s.memory.targetId=void 0)}else s.memory.targetId=void 0}else{const e=s.room.find(FIND_MY_STRUCTURES,{filter:t=>(t.structureType===STRUCTURE_EXTENSION||t.structureType===STRUCTURE_SPAWN||t.structureType===STRUCTURE_STORAGE)&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0});e.length>0&&(s.memory.targetId=e[0].id)}},L=s=>{const e=s.room.controller;e&&s.upgradeController(e)===ERR_NOT_IN_RANGE&&s.moveTo(e)},Q=s=>{const e=s.pos.findClosestByPath(FIND_CONSTRUCTION_SITES);e&&s.build(e)===ERR_NOT_IN_RANGE&&s.moveTo(e)},V=s=>{const e=s.room.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER&&t.store.getFreeCapacity(RESOURCE_ENERGY)>0});if(e.length>0){const t=e[0];s.transfer(t,RESOURCE_ENERGY)===ERR_NOT_IN_RANGE&&s.moveTo(t)}},te=s=>{const e=s.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER});for(const t of e){const o=t.pos.findClosestByRange(FIND_HOSTILE_CREEPS);if(o){t.attack(o);continue}const r=t.pos.findClosestByRange(FIND_MY_CREEPS,{filter:i=>i.hits<i.hitsMax});if(r){t.heal(r);continue}const n=t.pos.findClosestByRange(FIND_STRUCTURES,{filter:i=>i.hits<i.hitsMax&&i.structureType!==STRUCTURE_WALL&&i.structureType!==STRUCTURE_RAMPART});n&&t.repair(n)}};class j{constructor(){f(this,"queue",[]);global.taskSystem?this.queue=global.taskSystem.taskQueue:this.loadFromMemory()}loadFromMemory(){Memory.taskSystem.taskQueue&&(this.queue=Memory.taskSystem.taskQueue)}save(){global.taskSystem=h.merge(global.taskSystem,{taskQueue:this.queue}),Memory.taskSystem.taskQueue=this.queue}hasTask(e){return this.queue.some(t=>t.id===e)}init(){this.queue=[],this.save()}add(e){this.queue.some(t=>t.id===e.id)||this.queue.push(e)}pop(){const e=this.queue.shift();return e&&this.save(),e}remove(e){const t=this.queue.findIndex(o=>o.id===e);return t!==-1?(this.queue.splice(t,1),!0):!1}updateTask(e,t){const o=this.queue.find(r=>r.id===e);return o?(Object.assign(o,t),!0):!1}size(){return this.queue.length}peek(){return this.queue[0]}getAll(){return[...this.queue]}getByStatus(e){return this.queue.filter(t=>t.status===e)}getByType(e){return this.queue.filter(t=>t.type===e)}cleanupExpired(e=1e3){const t=Game.time,o=this.queue.length;return this.queue=this.queue.filter(r=>!(r.timestamp&&t-r.timestamp>e)),o-this.queue.length}getStats(){const e={total:this.queue.length,published:0,assigned:0,completed:0,expired:0,byType:{}};for(const t of this.queue){const o=t.status||"unknown";o in e&&e[o]++;const r=t.type;e.byType[r]=(e.byType[r]||0)+1}return e}reload(){this.loadFromMemory()}}new j;class oe{constructor(e){f(this,"taskQueue");this.taskQueue=e}assignTasks(e){const t=Object.values(Game.creeps).filter(r=>r.room.name===e.name),o=this.taskQueue.getByStatus("published");for(const r of t){if(r.memory.currentTask)continue;const n=o.find(i=>i.allowedCreepRoles.includes(r.memory.role||""));n&&(r.memory.currentTask=n.id,this.taskQueue.updateTask(n.id,{status:"assigned",assignedTo:[...n.assignedTo||[],r.name]}),console.log(`[任务系统] 分配任务 ${n.id} 给 ${r.name}`))}}executeTasks(e){const t=Object.values(Game.creeps).filter(o=>o.room.name===e.name);for(const o of t){if(!o.memory.currentTask)continue;const r=this.taskQueue.getAll().find(i=>i.id===o.memory.currentTask);if(!r){delete o.memory.currentTask;continue}const n=this.executeTask(o,r);n==="completed"?(this.taskQueue.updateTask(r.id,{status:"completed"}),delete o.memory.currentTask,console.log(`[任务系统] 任务 ${r.id} 完成`)):n==="failed"&&(this.taskQueue.updateTask(r.id,{status:"published"}),delete o.memory.currentTask,console.log(`[任务系统] 任务 ${r.id} 失败，重新分配`))}}executeTask(e,t){switch(t.type){case"harvest":return this.executeHarvestTask(e,t);case"upgrade":return this.executeUpgradeTask(e,t);case"build":return this.executeBuildTask(e,t);case"repair":return this.executeRepairTask(e,t);case"transfer":return this.executeTransferTask(e,t);default:return"failed"}}executeHarvestTask(e,t){const o=Game.getObjectById(t.fromId);if(!o)return"failed";if(e.store.getFreeCapacity()===0||o.energy===0)return"completed";switch(e.harvest(o)){case OK:return"in_progress";case ERR_NOT_IN_RANGE:return e.moveTo(o),"in_progress";default:return"failed"}}executeUpgradeTask(e,t){const o=Game.getObjectById(t.fromId);if(!o)return"failed";if(e.store[RESOURCE_ENERGY]===0){const n=e.room.find(FIND_SOURCES)[0];return n&&e.moveTo(n),"in_progress"}switch(e.upgradeController(o)){case OK:return"in_progress";case ERR_NOT_IN_RANGE:return e.moveTo(o),"in_progress";default:return"failed"}}executeBuildTask(e,t){const o=Game.getObjectById(t.fromId);if(!o)return"completed";if(e.store[RESOURCE_ENERGY]===0){const n=e.room.find(FIND_SOURCES)[0];return n&&e.moveTo(n),"in_progress"}switch(e.build(o)){case OK:return"in_progress";case ERR_NOT_IN_RANGE:return e.moveTo(o),"in_progress";default:return"failed"}}executeRepairTask(e,t){const o=Game.getObjectById(t.fromId);if(!o)return"failed";if(o.hits===o.hitsMax)return"completed";if(e.store[RESOURCE_ENERGY]===0){const n=e.room.find(FIND_SOURCES)[0];return n&&e.moveTo(n),"in_progress"}switch(e.repair(o)){case OK:return"in_progress";case ERR_NOT_IN_RANGE:return e.moveTo(o),"in_progress";default:return"failed"}}executeTransferTask(e,t){const o=Game.getObjectById(t.toId);if(!o)return"failed";if("store"in o&&o.store.getFreeCapacity(RESOURCE_ENERGY)===0)return"completed";if(e.store[RESOURCE_ENERGY]===0){const n=e.room.find(FIND_SOURCES)[0];return n&&e.moveTo(n),"in_progress"}switch(e.transfer(o,RESOURCE_ENERGY)){case OK:return"in_progress";case ERR_NOT_IN_RANGE:return e.moveTo(o),"in_progress";default:return"failed"}}}class re{constructor(e){f(this,"taskQueue");this.taskQueue=e}monitorRoom(e){this.monitorTaskQueue(e),this.monitorCreepTasks(e),this.monitorRoomResources(e)}monitorTaskQueue(e){const t=this.taskQueue.getStats();Game.time%100===0&&(console.log(`[任务监控][${e.name}] 任务队列状态:`),console.log(`  - 总任务数: ${t.total}`),console.log(`  - 待分配: ${t.published}`),console.log(`  - 执行中: ${t.assigned}`),console.log(`  - 已完成: ${t.completed}`),console.log("  - 按类型:",JSON.stringify(t.byType))),global.taskSystem=h.merge(global.taskSystem,{taskQueue:this.taskQueue.getAll()}),Game.time%10===0&&(Memory.taskSystem.taskQueue=this.taskQueue.getAll())}monitorCreepTasks(e){const t=Object.values(Game.creeps).filter(n=>n.room.name===e.name),o=t.filter(n=>n.memory.currentTask),r=t.filter(n=>!n.memory.currentTask);if(Game.time%100===0){console.log(`[任务监控][${e.name}] Creep任务状态:`),console.log(`  - 总creep数: ${t.length}`),console.log(`  - 有任务: ${o.length}`),console.log(`  - 空闲: ${r.length}`);const n={};for(const i of t){const a=i.memory.role||"unknown";n[a]||(n[a]={total:0,withTask:0}),n[a].total++,i.memory.currentTask&&n[a].withTask++}for(const[i,a]of Object.entries(n))console.log(`    ${i}: ${a.withTask}/${a.total}`)}}monitorRoomResources(e){const t=e.energyAvailable,o=e.energyCapacityAvailable,r=e.find(FIND_SOURCES),n=e.find(FIND_CONSTRUCTION_SITES),i=e.find(FIND_STRUCTURES,{filter:a=>a.hits<a.hitsMax});Game.time%100===0&&(console.log(`[任务监控][${e.name}] 资源状态:`),console.log(`  - 能量: ${t}/${o}`),console.log(`  - 能量源: ${r.length}个`),console.log(`  - 建筑工地: ${n.length}个`),console.log(`  - 受损建筑: ${i.length}个`))}getTaskStats(){const e=this.taskQueue.getStats();return{totalTasks:e.total,publishedTasks:e.published,assignedTasks:e.assigned,completedTasks:e.completed,taskTypes:e.byType}}getPerformanceMetrics(){const e=this.taskQueue.getAll(),t=Object.values(Game.creeps),o=e.filter(d=>d.status==="completed").length,r=e.length,n=r>0?o/r:0,i=e.filter(d=>d.status==="completed"&&d.timestamp),a=i.reduce((d,y)=>d+(Game.time-(y.timestamp||0)),0),l=i.length>0?a/i.length:0,R=t.filter(d=>d.memory.currentTask).length,u=t.length>0?(t.length-R)/t.length:0;return{taskCompletionRate:n,averageTaskDuration:l,idleCreepRate:u}}}class se{constructor(e){f(this,"taskQueue");this.taskQueue=e}publishTasks(e){this.publishHarvestTasks(e),this.publishUpgradeTasks(e),this.publishBuildTasks(e),this.publishRepairTasks(e),this.publishTransferTasks(e)}publishHarvestTasks(e){const t=e.find(FIND_SOURCES);for(const o of t)if(o.energy>0){const r=`harvest_${o.id}`;if(!this.taskQueue.hasTask(r)){const n={id:r,type:"harvest",fromId:o.id,toId:"",action:"harvest",allowedCreepRoles:["harvester","miner"],payload:{resourceType:RESOURCE_ENERGY,amount:o.energy},timestamp:Game.time,status:"published",room:e.name};this.taskQueue.add(n)}}}publishUpgradeTasks(e){if(e.controller&&e.controller.my){const t=`upgrade_${e.controller.id}`;if(!this.taskQueue.hasTask(t)){const o={id:t,type:"upgrade",fromId:e.controller.id,toId:e.controller.id,action:"upgradeController",allowedCreepRoles:["upgrader"],payload:{progress:e.controller.progress,progressTotal:e.controller.progressTotal,level:e.controller.level},timestamp:Game.time,status:"published",room:e.name};this.taskQueue.add(o)}}}publishBuildTasks(e){const t=e.find(FIND_CONSTRUCTION_SITES);for(const o of t){const r=`build_${o.id}`;if(!this.taskQueue.hasTask(r)){const n={id:r,type:"build",fromId:o.id,toId:o.id,action:"build",allowedCreepRoles:["builder"],payload:{structureType:o.structureType,progress:o.progress,progressTotal:o.progressTotal},timestamp:Game.time,status:"published",room:e.name};this.taskQueue.add(n)}}}publishRepairTasks(e){const t=e.find(FIND_STRUCTURES,{filter:o=>o.hits<o.hitsMax&&o.structureType!==STRUCTURE_WALL&&o.structureType!==STRUCTURE_RAMPART});for(const o of t){const r=`repair_${o.id}`;if(!this.taskQueue.hasTask(r)){const n={id:r,type:"repair",fromId:o.id,toId:o.id,action:"repair",allowedCreepRoles:["repairer","builder"],payload:{structureType:o.structureType,hits:o.hits,hitsMax:o.hitsMax},timestamp:Game.time,status:"published",room:e.name};this.taskQueue.add(n)}}}publishTransferTasks(e){const t=e.find(FIND_MY_STRUCTURES,{filter:o=>"store"in o&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of t){const r=`transfer_${o.id}`;if(!this.taskQueue.hasTask(r)){const n={id:r,type:"transfer",fromId:"",toId:o.id,action:"transfer",allowedCreepRoles:["harvester"],payload:{resourceType:RESOURCE_ENERGY,amount:o.store.getFreeCapacity(RESOURCE_ENERGY)},timestamp:Game.time,status:"published",room:e.name};this.taskQueue.add(n)}}}}class ne{constructor(){f(this,"taskQueue");f(this,"publisher");f(this,"executor");f(this,"monitor");Memory.taskSystem?console.log(Memory.taskSystem):(console.log("Memory.taskSystem is not defined, initializing..."),Memory.taskSystem={taskQueue:[]}),this.taskQueue=new j,this.publisher=new se(this.taskQueue),this.executor=new oe(this.taskQueue),this.monitor=new re(this.taskQueue)}run(e){this.monitor.monitorRoom(e),this.publisher.publishTasks(e)}cleanupExpiredTasks(){const e=this.taskQueue.cleanupExpired(1e3);e>0&&console.log(`[任务系统] 清理了 ${e} 个过期任务`)}getStatus(){const e=this.taskQueue.getStats();return{totalTasks:e.total,publishedTasks:e.published,assignedTasks:e.assigned,completedTasks:e.completed}}}const ie=new ne;function ae(s){ie.run(s)}const ue=[S.SourceLink];class le{constructor(e){f(this,"link");this.link=e}run(){if(this.canSend(1)&&ue.includes(this.link.id)){const e=Game.getObjectById(S.ControllerLink);e&&e.store.getFreeCapacity(RESOURCE_ENERGY)>0&&this.send(e)}}canSend(e=200){return this.link.cooldown===0&&this.link.store[RESOURCE_ENERGY]>=e}send(e,t){if(this.link.cooldown>0)return ERR_TIRED;const o=t?Math.min(t,this.link.store[RESOURCE_ENERGY]):this.link.store[RESOURCE_ENERGY];return this.link.transferEnergy(e,o)}get energy(){return this.link.store[RESOURCE_ENERGY]}get energyCapacity(){return this.link.store.getCapacity(RESOURCE_ENERGY)||800}get isFull(){return this.energy>=this.energyCapacity}get isEmpty(){return this.energy===0}}const me=s=>{s.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_LINK}).forEach(t=>{new le(t).run()})},E={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡"},g=(s,e,t={})=>{const{time:o=Game.time}=t;o%s===0&&e()},Re=(s,e,t=!0)=>{const o={road:1,plain:2,swamp:10};let r=0,n=0;for(const u of s)if(u===MOVE)r++;else{if(u===CARRY&&!t)continue;n++}const i=n*o[e],a=r*2,l=a/(i||1),R=Math.ceil(1/l);return{movePerTick:Math.min(l,1),ticksPerMove:R,fatiguePerTick:i,fatigueRecover:a,moveCount:r,fatigueParts:n}};function ce(s,e,t=1,o=Game.rooms[c.MainRoom]){if(!o)return[];const r={};return o.lookAtArea(e-t,s-t,e+t,s+t,!0).forEach(n=>{if(r[`${n.x},${n.y}`]!==!1){if(r[`${n.x},${n.y}`]===void 0&&(r[`${n.x},${n.y}`]=!0),n.type==="terrain"&&n.terrain==="wall"){r[`${n.x},${n.y}`]=!1;return}if(n.type==="creep"){r[`${n.x},${n.y}`]=!1;return}if(n.type==="structure"&&n.structure){!(n.structure instanceof StructureContainer)&&!(n.structure instanceof StructureRoad)&&!(n.structure instanceof StructureRampart)&&(r[`${n.x},${n.y}`]=!1);return}}}),Object.entries(r).filter(([n,i])=>i===!0).map(([n])=>{const[i,a]=n.split(",");return{x:Number(i),y:Number(a)}})}function U(s,e,t){return e==="terminal"?s.room.terminal?[s.room.terminal]:null:e==="miner"?t?s.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):e==="link"?t?s.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="link"&&o.store[RESOURCE_ENERGY]>0&&o.id!==S.SourceLink}):s.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="link"&&o.store[RESOURCE_ENERGY]>0&&o.id!==S.SourceLink}):e==="container"?t?s.pos.findClosestByRange(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):e==="storage"?t?s.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):e==="source"?t?s.pos.findClosestByRange(FIND_SOURCES,{filter:o=>o.energy>0}):s.room.find(FIND_SOURCES,{filter:o=>o.energy>0}):e==="mineral"?t?s.pos.findClosestByRange(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):s.room.find(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):e==="ruin"?t?s.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="tombstone"?t?s.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):e==="resource"?t?s.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):s.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}class m{constructor(e){f(this,"role");this.role=e}getEnergyFromStore(e,t,o=null){var n,i,a;let r=o;if(!r)if(t.length===1){const[l]=t;l&&(r=U(e,l,!0))}else{const l=[];for(const u of t){const d=(i=(n=U(e,u,!1))==null?void 0:n.filter(y=>y!==null))!=null?i:[];for(const y of d)y&&l.push(y)}let R=[];if(r||(R=l.filter(u=>u instanceof StructureTerminal&&u.store[RESOURCE_ENERGY]>0),R.length>0&&(r=(a=R.pop())!=null?a:null)),r||(R=l.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),R.length>0&&(r=e.pos.findClosestByRange(R))),r||(R=l.filter(u=>u instanceof StructureLink&&u.store[RESOURCE_ENERGY]>0&&u.id===S.ControllerLink),R.length>0&&(r=e.pos.findClosestByRange(R))),r||(R=l.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),R.length>0&&(r=e.pos.findClosestByRange(R))),r||(R=l.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),R.length>0&&(r=e.pos.findClosestByRange(R))),!r){const u=l.filter(y=>y instanceof Creep&&y.memory&&y.memory.role==="miner"&&y.store[RESOURCE_ENERGY]>0),d=u.filter(y=>y.store.getFreeCapacity(RESOURCE_ENERGY)===0);d.length>0?r=e.pos.findClosestByRange(d):u.length>0&&(r=e.pos.findClosestByRange(u))}r||(R=l.filter(u=>e.pos.isNearTo(u.pos)?!0:u instanceof Source&&ce(u.pos.x,u.pos.y,1,e.room).length),R.length>0&&(r=e.pos.findClosestByRange(R))),r||(R=l.filter(u=>u instanceof Mineral||u instanceof Deposit),R.length>0&&(r=e.pos.findClosestByRange(R)))}if(r){if(!r.pos.isNearTo(e.pos))return e.moveTo(r,{visualizePathStyle:{stroke:"#ffffff"}}),r;if(r instanceof Creep&&r.memory.role==="miner")return r;if(r instanceof Source||r instanceof Mineral)return e.harvest(r),g(10,()=>e.say(E.harvesting),{time:e.ticksToLive}),r;if(r instanceof Ruin||r instanceof Tombstone)return e.withdraw(r,RESOURCE_ENERGY),g(10,()=>e.say(E.withdrawing),{time:e.ticksToLive}),r;if(r instanceof StructureStorage||r instanceof StructureContainer||r instanceof StructureLink||r instanceof StructureTerminal)return e.withdraw(r,RESOURCE_ENERGY),g(10,()=>e.say(E.withdrawing),{time:e.ticksToLive}),r;if(r instanceof Resource)return e.pickup(r),g(10,()=>e.say(E.picking),{time:e.ticksToLive}),r}return r}getAllAvailableStores(e,t){const o=[];for(const r of t){const n=U(e,r,!1);for(const i of n)i&&o.push(i)}return o}}f(m,"generatorRoleBody",e=>e.reduce((t,{body:o,count:r})=>t.concat(Array(r).fill(o)),[]));const I=[STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_ROAD],C=class C extends m{constructor(){super(C.role)}create(e){const{baseId:t=T.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="repairing"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["storage","resource","ruin","tombstone","terminal","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_STRUCTURES,{filter:r=>!!(I.includes(r.structureType)&&r.hits<r.hitsMax||r instanceof StructureTower&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((r,n)=>{const i=I.indexOf(r.structureType),a=I.indexOf(n.structureType);return i!==a?i-a:r.hits-n.hits});if(!t.length)return;const o=t[0];if(!o.pos.isNearTo(e))e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});else if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)e.transfer(o,RESOURCE_ENERGY)===OK&&g(5,()=>e.say(E.transferring));else switch(e.repair(o)){case ERR_NOT_IN_RANGE:{e.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};f(C,"role","repairer");let v=C;const q=new v,$=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],b=class b extends m{constructor(){super(b.role)}create(e){const{baseId:t=T.MainBase,body:o,name:r,memoryRoleOpts:n={role:"builder",task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="building"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","terminal","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,r)=>{const n=$.indexOf(o.structureType),i=$.indexOf(r.structureType);return n-i});if(t.length>0)switch(e.memory.task==="repairing"&&(e.memory.task="building"),e.build(t[0])){case ERR_NOT_IN_RANGE:{e.moveTo(t[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{g(10,()=>e.say(E.building),{time:e.ticksToLive});break}}else e.memory.task="repairing",this.roleTask2(e)}roleTask2(e){q.run(e)}};f(b,"role","builder");let Y=b;const fe=new Y,G=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],O=class O extends m{constructor(){super(O.role);f(this,"create",t=>{const{baseId:o=T.MainBase,body:r,name:n,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=t,a=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(r,a,{memory:i})})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task==="transferring"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="transferring"),t.memory.task==="harvesting"?this.harvestTask(t):this.roleTask(t)}roleTask(t,o=t.room){const r=o.find(FIND_MY_STRUCTURES,{filter:n=>G.includes(n.structureType)&&"store"in n&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((n,i)=>{if(n.structureType===i.structureType)return n.pos.getRangeTo(t.pos)-i.pos.getRangeTo(t.pos);const a=G.indexOf(n.structureType),l=G.indexOf(i.structureType);return a-l});if(r.length>0){const n=[];for(const[i,a]of Object.entries(t.store))a>0&&n.push(i);for(const i of n)switch(t.transfer(r[0],i)){case ERR_NOT_IN_RANGE:{t.moveTo(r[0],{visualizePathStyle:{stroke:"#ffffff"}});break}}}}harvestTask(t){let o=!1;t.room.find(FIND_MY_CREEPS).every(n=>n.name.startsWith("Min"))&&(o=!0);const r=["resource","ruin","tombstone","container","miner"];t.body.some(n=>n.type===WORK)&&r.push("source"),o&&r.unshift("storage"),this.getEnergyFromStore(t,r)}};f(O,"role","harvester");let A=O;const H=new A,p=class p extends m{constructor(){super(p.role)}create(e){const{baseId:t=T.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"moving"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.memory.task==="moving"?this.moveTask(e):this.roleTask(e)}moveTask(e){var o;let t=null;e.memory.targetId?t=Game.getObjectById(e.memory.targetId):t=(o=Memory.sources.Source.map(n=>Game.getObjectById(n)).filter(n=>n!==null).find(n=>n.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==e.name}).length===0))!=null?o:null,t&&(e.pos.isNearTo(t)?(e.memory.task="mining",e.memory.targetId=t.id):(e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}}),g(10,()=>e.say(E.moving))))}roleTask(e){if(e.memory.targetId){const o=Game.getObjectById(e.memory.targetId);o?(e.harvest(o),g(10,()=>e.say(E.mining))):e.memory.task="moving"}if(e.store[RESOURCE_ENERGY]!==0){const o=Game.getObjectById(S.SourceLink);o!=null&&o.pos.isNearTo(e)&&(e.transfer(o,RESOURCE_ENERGY),g(10,()=>e.say(E.transferring)))}const t=e.pos.findInRange(FIND_MY_CREEPS,1,{filter:o=>o.memory.role!=="miner"&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of t)e.store[RESOURCE_ENERGY]>0&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(e.transfer(o,RESOURCE_ENERGY),g(10,()=>e.say(E.transferring)))}};f(p,"role","miner");let w=p;const de=new w,_=class _ extends m{constructor(){super(_.role);f(this,"create",t=>{var l;const{baseId:o=T.MainBase,body:r,name:n,memoryRoleOpts:i}=t,a=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(r,a,{memory:{role:"pioneer",task:"harvesting",targetRoomName:(l=i==null?void 0:i.targetRoomName)!=null?l:c.TargetRoomFlag,...i}})})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="pioneering"),t.memory.task==="harvesting"&&this.harvestingTask(t),this.buildingTask(t),t.memory.task==="pioneering"&&this.roleTask(t)}harvestingTask(t){var r,n;let o=null;if(t.memory.targetRoomName&&Game.rooms[t.memory.targetRoomName])o=Game.rooms[t.memory.targetRoomName];else{const i=Game.flags[(r=t.memory.targetRoomName)!=null?r:c.TargetRoomFlag];if(!i){this.getEnergyFromStore(t,["source"]);return}if(i.room)o=i.room;else{t.moveTo(i,{visualizePathStyle:{stroke:"#ffaa00"}}),g(10,()=>t.say(E.moving),{time:t.ticksToLive});return}}if(o){if(t.room.name===o.name)if(t.ticksToLive&&t.ticksToLive%10===0||!t.memory.cacheTargetStoreId){const i=this.getEnergyFromStore(t,["resource","ruin","tombstone","source"]);t.memory.cacheTargetStoreId=i==null?void 0:i.id}else{const i=(n=Game.getObjectById(t.memory.cacheTargetStoreId))!=null?n:null;this.getEnergyFromStore(t,["resource","ruin","tombstone","source"],i)}else t.moveTo(o.find(FIND_SOURCES)[0],{visualizePathStyle:{stroke:"#ffaa00"}}),g(10,()=>t.say(E.moving),{time:t.ticksToLive});t.memory.targetRoomName=o.name}}buildingTask(t){const o=t.pos.findInRange(FIND_STRUCTURES,1,{filter:n=>n.structureType===STRUCTURE_ROAD&&n.hits<n.hitsMax}),r=t.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:n=>n.structureType===STRUCTURE_ROAD});r.length&&t.build(r[0]),o.length&&t.repair(o[0])}roleTask(t){H.roleTask(t,Game.rooms[c.MainRoom])}};f(_,"role","pioneer");let F=_;const ye=new F,N=class N extends m{constructor(){super(N.role)}create(e){const{baseId:t=T.MainBase,body:o,name:r,memoryRoleOpts:n={role:this.role,task:"harvesting"}}=e,i=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[t].spawnCreep(o,i,{memory:n})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="upgrading"),e.memory.task==="harvesting"?this.getEnergyFromStore(e,["resource","ruin","tombstone","link","terminal","storage","container","miner","source"]):this.roleTask(e)}roleTask(e){const t=e.room.controller;if(!t)return;switch(e.upgradeController(t)){case ERR_NOT_IN_RANGE:{e.moveTo(t,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{g(10,()=>e.say(E.upgrading),{time:e.ticksToLive});break}}}};f(N,"role","upgrader");let B=N;const ge=new B,x={harvester:H,builder:fe,miner:de,upgrader:ge,repairer:q,pioneer:ye};class Ee{constructor(e){f(this,"tower");this.tower=e}run(){var e,t;this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps()||this.tower.store[RESOURCE_ENERGY]>((e=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?e:0)*.6&&this.healFriendlyCreeps()||this.tower.store[RESOURCE_ENERGY]>((t=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?t:0)*.6&&this.repairStructures())}attackHostileCreeps(){const e=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:t=>t.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK||o.type===HEAL)||this.tower.pos.getRangeTo(t)<=10});if(e.length>0){const t=e.sort((o,r)=>o.hits-o.hitsMax)[0];return this.tower.attack(t),!0}return!1}healFriendlyCreeps(){const e=this.tower.room.find(FIND_MY_CREEPS,{filter:t=>t.hits<t.hitsMax});if(e.length>0){const t=e.sort((o,r)=>o.hits-r.hits)[0];return this.tower.heal(t),!0}return!1}repairStructures(){const e=this.tower.room.find(FIND_STRUCTURES,{filter:t=>t.hits===t.hitsMax?!1:t instanceof StructureRoad&&t.hits<t.hitsMax*.6||t instanceof StructureContainer&&t.hits<t.hitsMax*.6||t instanceof StructureRampart&&t.hits<t.hitsMax*.03||t instanceof StructureWall&&this.tower.pos.getRangeTo(t)<=6&&t.hits<t.hitsMax*5e-4?!0:t.hits<1e5});if(e.length>0){const t=e.sort((o,r)=>o instanceof StructureRoad?-1:r instanceof StructureRoad?1:o instanceof StructureContainer?-1:r instanceof StructureContainer?1:o instanceof StructureRampart?-1:r instanceof StructureRampart?1:o.hits-r.hits)[0];return this.tower.repair(t),!0}return!1}}const Te=s=>{s.find(FIND_MY_STRUCTURES,{filter:t=>t.structureType===STRUCTURE_TOWER}).forEach(t=>{new Ee(t).run()})},he=()=>{Se(),Ce(),ke()},Se=()=>{for(let s in Memory.creeps)Game.creeps[s]||(delete Memory.creeps[s],console.log("Clearing non-existing creep memory:",s))},ke=()=>{var e;const s={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const t of Object.values(Game.creeps))t.room.name===c.MainRoom&&(t.name.startsWith("Min")||t.memory.role&&(s[t.memory.role]=((e=s[t.memory.role])!=null?e:0)+1));Memory.creepsCount=s},Ce=()=>{be()},be=(s=Game.rooms[c.MainRoom])=>{var r,n;if(!s)return;if(!((r=Memory.sources)!=null&&r.Source)){const i=s.find(FIND_SOURCES);h.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((n=Memory.sources)!=null&&n.Mineral)){const i=s.find(FIND_MINERALS);h.set(Memory,"sources.Mineral",i.map(a=>a.id))}const e=s.find(FIND_DROPPED_RESOURCES);h.set(Memory,"sources.Resource",e.map(i=>i.id));const t=s.find(FIND_RUINS);h.set(Memory,"sources.Ruin",t.map(i=>i.id));const o=s.find(FIND_TOMBSTONES);h.set(Memory,"sources.Tombstone",o.map(i=>i.id))},Oe=()=>{g(10,pe)},pe=()=>{const s=Object.values(Game.spawns);for(const e of s)e!=null&&e.spawning?e.room.visual.text(`Spawning:${e.spawning.name}`,{...e.pos,y:e.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):e.room.visual.text(`${e.store[RESOURCE_ENERGY]}`,{...e.pos,y:e.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},_e={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},Ne={roleMonitor:{harvester:{count:5,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},Me={roleMonitor:{harvester:{count:6,body:m.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},Ue={roleMonitor:{harvester:{count:4,body:m.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},Ie={roleMonitor:{harvester:{count:1,body:m.generatorRoleBody([{body:CARRY,count:17},{body:MOVE,count:9}])},pioneer:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:4,body:m.generatorRoleBody([{body:WORK,count:4},{body:CARRY,count:12},{body:MOVE,count:4}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},Ge={roleMonitor:{harvester:{count:1,body:m.generatorRoleBody([{body:CARRY,count:23},{body:MOVE,count:12}])},pioneer:{count:0,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:1,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:m.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:2,body:m.generatorRoleBody([{body:WORK,count:10},{body:CARRY,count:7},{body:MOVE,count:9}])},repairer:{count:1,body:m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},ve=[Ne,Me,Ue,Ie,Ge],Ye=s=>{for(let e=s;e>=1;e--){const t=ve[e-1];if(t)return t}return _e},Ae=()=>{we()},we=()=>{var o,r,n,i;const s=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const l=Game.creeps[a];l.room.name===c.MainRoom&&l.memory.role&&!l.name.startsWith("Min")&&s.set(l.memory.role,((o=s.get(l.memory.role))!=null?o:0)+1)}const e=Ye((n=(r=Game.rooms[c.MainRoom].controller)==null?void 0:r.level)!=null?n:0),t=s.entries();for(let[a,l]of t)if(e.roleMonitor[a]&&l<e.roleMonitor[a].count){g(10,()=>{var d,y,D;const R=((d=e.roleMonitor[a])==null?void 0:d.body)||[],u={};for(const P of R)u[P]=(u[P]||0)+1;console.log(`${a} 现有:${l} 需要:${(D=(y=e.roleMonitor[a])==null?void 0:y.count)!=null?D:0} Body:${JSON.stringify(u)}`)}),(i=x[a])==null||i.create({body:e.roleMonitor[a].body});break}},Fe=["MinMiner","MinMiner2"],Be=["MinPioneer","MinPioneer2","MinPioneer3"],xe=["MinPioneer4","MinPioneer5","MinPioneer6","MinPioneer7","MinPioneer15"],De=["MinPioneer8","MinPioneer9"],Pe=["MinPioneer10","MinPioneer11","MinPioneer12","MinPioneer13","MinPioneer14","MinPioneer15"],$e=()=>{je(),We(),Qe()},We=()=>{Ke()&&(Le(),Ae())},Ke=()=>{var t,o,r,n;const s=[...Fe.map(i=>({name:i,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"}];(t=Game.flags[c.TargetRoomFlag].room)!=null&&t.find(FIND_HOSTILE_CREEPS).length||s.push(...De.map(i=>({name:i,role:"pioneer",body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:4}]),memoryRoleOpts:{targetRoomName:c.TargetRoomFlag}}))),(o=Game.flags[c.TargetRoomFlag2].room)!=null&&o.find(FIND_HOSTILE_CREEPS).length||s.push(...xe.map(i=>({name:i,role:"pioneer",memoryRoleOpts:{targetRoomName:c.TargetRoomFlag2}}))),(r=Game.flags[c.TargetRoomFlag4].room)!=null&&r.find(FIND_HOSTILE_CREEPS).length||s.push(...Pe.map(i=>({name:i,role:"pioneer",body:m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:4}]),memoryRoleOpts:{targetRoomName:c.TargetRoomFlag4}})));const e=Object.values(Game.creeps).filter(i=>i.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const i of s){if(i.name.startsWith("MinMiner")&&e>=2||i.name.startsWith("MinHarvester")&&Memory.creepsCount.harvester>0||i.name.startsWith("MinUpgrader")&&Memory.creepsCount.upgrader>0||i.name.startsWith("MinBuilder")&&Memory.creepsCount.builder>0||i.name.startsWith("MinRepairer")&&Memory.creepsCount.repairer>0)continue;if(!Game.creeps[i.name]){let l=[];switch(i.role){case"miner":{l=m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}case"pioneer":{l=Be.includes(i.name)?m.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:6},{body:MOVE,count:4}]):m.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:7},{body:MOVE,count:5}]);break}default:{l=m.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}if((n=Game.spawns[c.MainRoom2])!=null&&n.spawning)return!1;switch(utils.role2[i.role].create({body:l,name:i.name,memoryRoleOpts:i.memoryRoleOpts})){case OK:{console.log(`MiniGroup 正在孵化${i.name}`);break}}return!1}}return!0},Le=()=>{if(!Memory.creepsCount.miner)return;const s=Memory.creepsCount.miner,e=Object.values(Game.creeps).filter(o=>o.name.startsWith("MinMiner")),t=e.slice(0,2-s);for(const o of e)t.includes(o)||(o.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${o.name}`))},Qe=()=>{if(Game.cpu.bucket>=1e4){const s=Game.cpu.generatePixel();s===OK?console.log("生成 1 pixel",s):console.log("生成 pixel 失败",s)}},Ve=[{name:"CombatAttacker1",role:"attacker"},{name:"CombatAttacker2",role:"attacker"}],je=()=>{for(const t of Ve)if(!Game.creeps[t.name]){const r=m.generatorRoleBody([{body:ATTACK,count:1},{body:MOVE,count:1}]),n=Game.spawns[T.MainBase];if(n&&!n.spawning&&n.spawnCreep(r,t.name)===OK){console.log(`战斗小组正在孵化: ${t.name}`);break}}const s=[Game.creeps.CombatAttacker1,Game.creeps.CombatAttacker2],e=[{x:31,y:0},{x:0,y:31}];s.forEach((t,o)=>{if(!t)return;t.pos.x!==0&&t.pos.x!==49&&t.pos.y!==0&&t.pos.y!==49&&t.moveTo(e[o].x,e[o].y);const r=t.pos.findInRange(FIND_HOSTILE_CREEPS,1);r.length>0&&t.attack(r[0])})},qe=()=>{Oe(),he(),$e();const s=Game.rooms[c.MainRoom].find(FIND_HOSTILE_CREEPS);s.length>0&&console.log("有敌人",s)},He=()=>{var s,e,t,o;for(let r in Game.rooms){const n=Game.rooms[r];if(n.name===c.MainRoom2)X(),ae(n);else if((s=n.controller)!=null&&s.my){qe();const i=Object.values(Game.creeps).filter(a=>a.room.name!==c.MainRoom2);for(let a of i){if(a.room.name!==c.MainRoom&&a.memory.role!=="pioneer"){(e=Game.rooms[c.MainRoom].controller)!=null&&e.pos&&a.moveTo((t=Game.rooms[c.MainRoom].controller)==null?void 0:t.pos);continue}a.memory.role&&((o=x[a.memory.role])==null||o.run(a))}Te(n),me(n)}}};module.exports={loop:He};global.utils={role2:x,ticksPerMove:Re};
