"use strict";var x=Object.defineProperty;var K=(s,t,e)=>t in s?x(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var y=(s,t,e)=>K(s,typeof t!="symbol"?t+"":t,e);const g=require("lodash");var E=(s=>(s.MainBase="Spawn1",s))(E||{}),c=(s=>(s.MainRoom="E49S54",s.TargetRoomFlag="E48S54",s.TargetRoomFlag2="E48S55",s))(c||{});Object.values(E);const f={mining:"⛏️",harvesting:"⛏️",picking:"⛏️",withdrawing:"📥",transferring:"🔄",moving:"🚶",building:"🚧",upgrading:"⚡",repairing:"🔧"},m=(s,t,e={})=>{const{time:o=Game.time}=e;o%s===0&&t()},V=(s,t,e=!0)=>{const o={road:1,plain:2,swamp:10};let n=0,r=0;for(const l of s)if(l===MOVE)n++;else{if(l===CARRY&&!e)continue;r++}const i=r*o[t],a=n*2,u=a/(i||1),d=Math.ceil(1/u);return{movePerTick:Math.min(u,1),ticksPerMove:d,fatiguePerTick:i,fatigueRecover:a,moveCount:n,fatigueParts:r}};function L(s,t,e=1,o=Game.rooms[c.MainRoom]){if(!o)return[];const n={};return o.lookAtArea(t-e,s-e,t+e,s+e,!0).forEach(r=>{if(n[`${r.x},${r.y}`]!==!1){if(n[`${r.x},${r.y}`]===void 0&&(n[`${r.x},${r.y}`]=!0),r.type==="terrain"&&r.terrain==="wall"){n[`${r.x},${r.y}`]=!1;return}if(r.type==="creep"){n[`${r.x},${r.y}`]=!1;return}if(r.type==="structure"&&r.structure){!(r.structure instanceof StructureContainer)&&!(r.structure instanceof StructureRoad)&&!(r.structure instanceof StructureRampart)&&(n[`${r.x},${r.y}`]=!1);return}}}),Object.entries(n).filter(([r,i])=>i===!0).map(([r])=>{const[i,a]=r.split(",");return{x:Number(i),y:Number(a)}})}function M(s,t,e){return t==="miner"?e?s.pos.findClosestByRange(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_CREEPS,{filter:o=>o.memory.role==="miner"&&o.store[RESOURCE_ENERGY]>0}):t==="container"?e?s.pos.findClosestByRange(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_STRUCTURES,{filter:o=>o.structureType==="container"&&o.store[RESOURCE_ENERGY]>0}):t==="storage"?e?s.pos.findClosestByRange(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_MY_STRUCTURES,{filter:o=>o.structureType==="storage"&&o.store[RESOURCE_ENERGY]>0}):t==="source"?e?s.pos.findClosestByRange(FIND_SOURCES,{filter:o=>o.energy>0}):s.room.find(FIND_SOURCES,{filter:o=>o.energy>0}):t==="mineral"?e?s.pos.findClosestByRange(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):s.room.find(FIND_MINERALS,{filter:o=>o.mineralAmount>0}):t==="ruin"?e?s.pos.findClosestByRange(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_RUINS,{filter:o=>o.store[RESOURCE_ENERGY]>0}):t==="tombstone"?e?s.pos.findClosestByRange(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):s.room.find(FIND_TOMBSTONES,{filter:o=>o.store[RESOURCE_ENERGY]>0}):t==="resource"?e?s.pos.findClosestByRange(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):s.room.find(FIND_DROPPED_RESOURCES,{filter:o=>o.amount>0}):null}class R{constructor(t){y(this,"role");this.role=t}getEnergyFromStore(t,e){var n,r;let o=null;if(e.length===1){const[i]=e;i&&(o=M(t,i,!0))}else{const i=[];for(const u of e){const d=(r=(n=M(t,u,!1))==null?void 0:n.filter(l=>l!==null))!=null?r:[];for(const l of d)l&&i.push(l)}let a=[];if(a=i.filter(u=>u instanceof Resource&&u.resourceType===RESOURCE_ENERGY||u instanceof Ruin||u instanceof Tombstone),a.length>0&&(o=t.pos.findClosestByRange(a)),o||(a=i.filter(u=>u instanceof StructureStorage&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=t.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof StructureContainer&&u.store[RESOURCE_ENERGY]>0),a.length>0&&(o=t.pos.findClosestByRange(a))),!o){const u=i.filter(l=>l instanceof Creep&&l.memory&&l.memory.role==="miner"&&l.store[RESOURCE_ENERGY]>0),d=u.filter(l=>l.store.getFreeCapacity(RESOURCE_ENERGY)===0);d.length>0?o=t.pos.findClosestByRange(d):u.length>0&&(o=t.pos.findClosestByRange(u))}o||(a=i.filter(u=>t.pos.isNearTo(u.pos)?!0:u instanceof Source&&L(u.pos.x,u.pos.y,1,t.room).length>1),a.length>0&&(o=t.pos.findClosestByRange(a))),o||(a=i.filter(u=>u instanceof Mineral||u instanceof Deposit),a.length>0&&(o=t.pos.findClosestByRange(a)))}if(o){if(!o.pos.isNearTo(t.pos))return t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}}),o;if(o instanceof Creep&&o.memory.role==="miner")return o;if(o instanceof Source||o instanceof Mineral)return t.harvest(o),m(10,()=>t.say(f.harvesting),{time:t.ticksToLive}),o;if(o instanceof Ruin||o instanceof Tombstone)return t.withdraw(o,RESOURCE_ENERGY),m(10,()=>t.say(f.withdrawing),{time:t.ticksToLive}),o;if(o instanceof StructureStorage||o instanceof StructureContainer)return t.withdraw(o,RESOURCE_ENERGY),m(10,()=>t.say(f.withdrawing),{time:t.ticksToLive}),o;if(o instanceof Resource)return t.pickup(o),m(10,()=>t.say(f.picking),{time:t.ticksToLive}),o}return o}getAllAvailableStores(t,e){const o=[];for(const n of e){const r=M(t,n,!1);for(const i of r)i&&o.push(i)}return o}}y(R,"generatorRoleBody",t=>t.reduce((e,{body:o,count:n})=>e.concat(Array(n).fill(o)),[]));const N=[STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_ROAD],C=class C extends R{constructor(){super(C.role)}create(t){const{baseId:e=E.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=t,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="repairing"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["storage","resource","ruin","tombstone","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.find(FIND_STRUCTURES,{filter:n=>!!(N.includes(n.structureType)&&n.hits<n.hitsMax||n instanceof StructureTower&&n.store.getFreeCapacity(RESOURCE_ENERGY)>0)}).sort((n,r)=>{const i=N.indexOf(n.structureType),a=N.indexOf(r.structureType);return i!==a?i-a:n.hits-r.hits});if(!e.length)return;const o=e[0];if(!o.pos.isNearTo(t))t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});else if(o.structureType===STRUCTURE_TOWER&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0)t.transfer(o,RESOURCE_ENERGY)===OK&&m(5,()=>t.say(f.transferring));else switch(t.repair(o)){case ERR_NOT_IN_RANGE:{t.moveTo(o,{visualizePathStyle:{stroke:"#ffffff"}});break}}}};y(C,"role","repairer");let k=C;const D=new k,W=[STRUCTURE_EXTENSION,STRUCTURE_TOWER,STRUCTURE_WALL,STRUCTURE_RAMPART,STRUCTURE_ROAD,STRUCTURE_CONTAINER],S=class S extends R{constructor(){super(S.role)}create(t){const{baseId:e=E.MainBase,body:o,name:n,memoryRoleOpts:r={role:"builder",task:"harvesting"}}=t,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="building"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.find(FIND_MY_CONSTRUCTION_SITES).sort((o,n)=>{const r=W.indexOf(o.structureType),i=W.indexOf(n.structureType);return r-i});if(e.length>0)switch(t.memory.task==="repairing"&&(t.memory.task="building"),t.build(e[0])){case ERR_NOT_IN_RANGE:{t.moveTo(e[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:{m(10,()=>t.say(f.building),{time:t.ticksToLive});break}}else t.memory.task="repairing",this.roleTask2(t)}roleTask2(t){D.run(t)}};y(S,"role","builder");let v=S;const j=new v,_=[STRUCTURE_EXTENSION,STRUCTURE_SPAWN,STRUCTURE_STORAGE,STRUCTURE_CONTAINER],T=class T extends R{constructor(){super(T.role);y(this,"create",e=>{const{baseId:o=E.MainBase,body:n,name:r,memoryRoleOpts:i={role:"harvester",task:"harvesting"}}=e,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(n,a,{memory:i})})}run(e){e.store[RESOURCE_ENERGY]===0&&e.memory.task==="transferring"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&e.memory.task==="harvesting"&&(e.memory.task="transferring"),e.memory.task==="harvesting"?this.harvestTask(e):this.roleTask(e)}roleTask(e,o=e.room){const n=o.find(FIND_MY_STRUCTURES,{filter:r=>_.includes(r.structureType)&&"store"in r&&r.store.getFreeCapacity(RESOURCE_ENERGY)>0}).sort((r,i)=>{if(r.structureType===i.structureType)return r.pos.getRangeTo(e.pos)-i.pos.getRangeTo(e.pos);const a=_.indexOf(r.structureType),u=_.indexOf(i.structureType);return a-u});if(n.length>0)switch(e.transfer(n[0],RESOURCE_ENERGY)){case ERR_NOT_IN_RANGE:{e.moveTo(n[0],{visualizePathStyle:{stroke:"#ffffff"}});break}default:m(10,()=>e.say(f.transferring),{time:e.ticksToLive})}}harvestTask(e){const o=["resource","ruin","tombstone","container","miner"];e.body.some(n=>n.type===WORK)&&o.push("source"),this.getEnergyFromStore(e,o)}};y(T,"role","harvester");let U=T;const P=new U,O=class O extends R{constructor(){super(O.role)}create(t){const{baseId:e=E.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"moving"}}=t,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.memory.task==="moving"?this.moveTask(t):this.roleTask(t)}moveTask(t){var o;let e=null;t.memory.targetId?e=Game.getObjectById(t.memory.targetId):e=(o=Memory.sources.Source.map(r=>Game.getObjectById(r)).filter(r=>r!==null).find(r=>r.pos.findInRange(FIND_MY_CREEPS,1,{filter:a=>a.memory.role==="miner"&&a.name!==t.name}).length===0))!=null?o:null,e&&(t.pos.isNearTo(e)?(t.memory.task="mining",t.memory.targetId=e.id):(t.moveTo(e,{visualizePathStyle:{stroke:"#ffffff"}}),m(10,()=>t.say(f.moving))))}roleTask(t){const e=t.pos.findInRange(FIND_MY_CREEPS,1,{filter:o=>o.memory.role!=="miner"&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0});for(const o of e)t.store[RESOURCE_ENERGY]>0&&o.store.getFreeCapacity(RESOURCE_ENERGY)>0&&(t.transfer(o,RESOURCE_ENERGY),m(10,()=>t.say(f.transferring)));if(t.memory.targetId){const o=Game.getObjectById(t.memory.targetId);o&&(t.harvest(o),m(10,()=>t.say(f.mining)));return}t.memory.task="moving"}};y(O,"role","miner");let G=O;const z=new G,b=class b extends R{constructor(){super(b.role);y(this,"create",e=>{var u;const{baseId:o=E.MainBase,body:n,name:r,memoryRoleOpts:i}=e,a=r!=null?r:`${this.role}-${Game.time}`;return Game.spawns[o].spawnCreep(n,a,{memory:{role:"pioneer",task:"harvesting",targetRoomName:(u=i==null?void 0:i.targetRoomName)!=null?u:c.TargetRoomFlag,...i}})})}run(e){var o;e.store[RESOURCE_ENERGY]===0&&e.memory.task!=="harvesting"&&(e.memory.task="harvesting"),e.store.getFreeCapacity()===0&&(e.room.name===((o=e.memory)==null?void 0:o.targetRoomName)&&e.memory.task==="harvesting"?e.memory.task="building":e.memory.task="pioneering"),e.store[RESOURCE_ENERGY]>0&&e.memory.task!=="harvesting"&&this.buildingTask(e),e.memory.task==="harvesting"?this.harvestingTask(e):e.memory.task==="building"?this.buildingTask(e):e.memory.task==="pioneering"&&this.roleTask(e)}harvestingTask(e){var n;let o=null;if(e.memory.targetRoomName&&Game.rooms[e.memory.targetRoomName])o=Game.rooms[e.memory.targetRoomName];else{const r=Game.flags[(n=e.memory.targetRoomName)!=null?n:c.TargetRoomFlag];if(!r){this.getEnergyFromStore(e,["source"]);return}if(r.room)o=r.room;else{e.moveTo(r,{visualizePathStyle:{stroke:"#ffaa00"}}),m(10,()=>e.say(f.moving),{time:e.ticksToLive});return}}o&&(e.room.name===o.name?this.getEnergyFromStore(e,["source"]):(e.moveTo(o.find(FIND_SOURCES)[0],{visualizePathStyle:{stroke:"#ffaa00"}}),m(10,()=>e.say(f.moving),{time:e.ticksToLive})),e.memory.targetRoomName=o.name)}buildingTask(e){const o=e.pos.findInRange(FIND_STRUCTURES,1,{filter:r=>r.structureType===STRUCTURE_ROAD}),n=e.pos.findInRange(FIND_CONSTRUCTION_SITES,1,{filter:r=>r.structureType===STRUCTURE_ROAD});if(n.length){const r=e.build(n[0]);r===ERR_NOT_IN_RANGE?e.moveTo(n[0]):r===OK&&m(10,()=>e.say(f.building),{time:e.ticksToLive});return}if(o.length&&o[0].hits<o[0].hitsMax){const r=e.repair(o[0]);r===ERR_NOT_IN_RANGE?e.moveTo(o[0]):r===OK&&m(10,()=>e.say(f.repairing),{time:e.ticksToLive});return}e.memory.task="pioneering"}roleTask(e){P.roleTask(e,Game.rooms[c.MainRoom])}};y(b,"role","pioneer");let I=b;const H=new I,h=class h extends R{constructor(){super(h.role)}create(t){const{baseId:e=E.MainBase,body:o,name:n,memoryRoleOpts:r={role:this.role,task:"harvesting"}}=t,i=n!=null?n:`${this.role}-${Game.time}`;return Game.spawns[e].spawnCreep(o,i,{memory:r})}run(t){t.store[RESOURCE_ENERGY]===0&&t.memory.task!=="harvesting"&&(t.memory.task="harvesting"),t.store.getFreeCapacity()===0&&t.memory.task==="harvesting"&&(t.memory.task="upgrading"),t.memory.task==="harvesting"?this.getEnergyFromStore(t,["resource","ruin","tombstone","storage","container","miner","source"]):this.roleTask(t)}roleTask(t){const e=t.room.controller;if(!e)return;switch(t.upgradeController(e)){case ERR_NOT_IN_RANGE:{t.moveTo(e,{visualizePathStyle:{stroke:"#ffffff"}});break}case OK:{m(10,()=>t.say(f.upgrading),{time:t.ticksToLive});break}}}};y(h,"role","upgrader");let Y=h;const Q=new Y,A={harvester:P,builder:j,miner:z,upgrader:Q,repairer:D,pioneer:H};class J{constructor(t){y(this,"tower");this.tower=t}run(){var t;this.tower.store[RESOURCE_ENERGY]!==0&&(this.attackHostileCreeps()||this.healFriendlyCreeps()||this.tower.store[RESOURCE_ENERGY]>((t=this.tower.store.getCapacity(RESOURCE_ENERGY))!=null?t:0)*.6&&this.repairStructures())}attackHostileCreeps(){const t=this.tower.room.find(FIND_HOSTILE_CREEPS,{filter:e=>e.body.some(o=>o.type===ATTACK||o.type===RANGED_ATTACK)});if(t.length>0){const e=t.sort((o,n)=>o.hits-o.hitsMax)[0];return this.tower.attack(e),!0}return!1}healFriendlyCreeps(){const t=this.tower.room.find(FIND_MY_CREEPS,{filter:e=>e.hits<e.hitsMax});if(t.length>0){const e=t.sort((o,n)=>o.hits-n.hits)[0];return this.tower.heal(e),!0}return!1}repairStructures(){const t=this.tower.room.find(FIND_STRUCTURES,{filter:e=>e.hits===e.hitsMax?!1:e instanceof StructureRoad&&e.hits<e.hitsMax*.6||e instanceof StructureContainer&&e.hits<e.hitsMax*.6||e instanceof StructureRampart&&e.hits<e.hitsMax*.05||e instanceof StructureWall&&e.hits<e.hitsMax*5e-4?!0:e.hits<1e5});if(t.length>0){const e=t.sort((o,n)=>o instanceof StructureRoad?-1:n instanceof StructureRoad?1:o instanceof StructureContainer?-1:n instanceof StructureContainer?1:o instanceof StructureRampart?-1:n instanceof StructureRampart?1:o.hits-n.hits)[0];return this.tower.repair(e),!0}return!1}}const X=s=>{s.find(FIND_MY_STRUCTURES,{filter:e=>e.structureType===STRUCTURE_TOWER}).forEach(e=>{new J(e).run()})},q=()=>{Z(),te(),ee()},Z=()=>{for(let s in Memory.creeps)Game.creeps[s]||(delete Memory.creeps[s],console.log("Clearing non-existing creep memory:",s))},ee=()=>{var t;const s={harvester:0,builder:0,upgrader:0,miner:0,minerStore:0,repairer:0,pioneer:0};for(const e of Object.values(Game.creeps))e.name.startsWith("Min")||e.memory.role&&(s[e.memory.role]=((t=s[e.memory.role])!=null?t:0)+1);Memory.creepsCount=s},te=()=>{oe()},oe=(s=Game.rooms[c.MainRoom])=>{var n,r;if(!s)return;if(!((n=Memory.sources)!=null&&n.Source)){const i=s.find(FIND_SOURCES);g.set(Memory,"sources.Source",i.map(a=>a.id))}if(!((r=Memory.sources)!=null&&r.Mineral)){const i=s.find(FIND_MINERALS);g.set(Memory,"sources.Mineral",i.map(a=>a.id))}const t=s.find(FIND_DROPPED_RESOURCES);g.set(Memory,"sources.Resource",t.map(i=>i.id));const e=s.find(FIND_RUINS);g.set(Memory,"sources.Ruin",e.map(i=>i.id));const o=s.find(FIND_TOMBSTONES);g.set(Memory,"sources.Tombstone",o.map(i=>i.id))},re=()=>{m(10,ne)},ne=()=>{const s=Object.values(Game.spawns);for(const t of s)t.spawning?t.room.visual.text(`Spawning:${t.spawning.name}`,{...t.pos,y:t.pos.y-1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1}):t.room.visual.text(`${t.store[RESOURCE_ENERGY]}`,{...t.pos,y:t.pos.y+1},{font:.5,color:"#00ff00",stroke:"#000000",strokeWidth:.1})},se={roleMonitor:{harvester:{count:4,body:[WORK,WORK,CARRY,MOVE]},builder:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},upgrader:{count:3,body:[WORK,CARRY,CARRY,MOVE,MOVE]},miner:{count:0,body:[WORK,WORK,WORK,WORK,CARRY,CARRY,CARRY,CARRY,MOVE]},pioneer:{count:1,body:[WORK,WORK,CARRY,CARRY,MOVE,MOVE]}}},ie={roleMonitor:{harvester:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},builder:{count:8,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},upgrader:{count:5,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}])},pioneer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:2},{body:MOVE,count:2}])}}},ae={roleMonitor:{harvester:{count:6,body:R.generatorRoleBody([{body:CARRY,count:7},{body:MOVE,count:4}])},builder:{count:2,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])},upgrader:{count:10,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:2},{body:MOVE,count:3}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:4},{body:MOVE,count:3}])}}},ue={roleMonitor:{harvester:{count:4,body:R.generatorRoleBody([{body:CARRY,count:11},{body:MOVE,count:5}])},builder:{count:3,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},upgrader:{count:6,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])},repairer:{count:5,body:R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:5},{body:MOVE,count:5}])}}},Re={roleMonitor:{harvester:{count:1,body:R.generatorRoleBody([{body:CARRY,count:17},{body:MOVE,count:9}])},pioneer:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},builder:{count:0,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:7},{body:MOVE,count:7}])},miner:{count:2,body:R.generatorRoleBody([{body:WORK,count:6},{body:CARRY,count:1},{body:MOVE,count:4}])},upgrader:{count:2,body:R.generatorRoleBody([{body:WORK,count:4},{body:CARRY,count:12},{body:MOVE,count:4}])},repairer:{count:1,body:R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:15},{body:MOVE,count:4}])}}},me=[ie,ae,ue,Re],le=s=>{for(let t=s;t>=1;t--){const e=me[t-1];if(e)return e}return se},fe=()=>{ye()},ye=()=>{var o,n,r,i;const s=new Map([["miner",0],["harvester",0],["minerStore",0],["builder",0],["upgrader",0],["repairer",0]]);for(let a in Game.creeps){const u=Game.creeps[a];u.memory.role&&!u.name.startsWith("Min")&&s.set(u.memory.role,((o=s.get(u.memory.role))!=null?o:0)+1)}const t=le((r=(n=Game.rooms[c.MainRoom].controller)==null?void 0:n.level)!=null?r:0),e=s.entries();for(let[a,u]of e)if(t.roleMonitor[a]&&u<t.roleMonitor[a].count){m(10,()=>{var p,w,F;const d=((p=t.roleMonitor[a])==null?void 0:p.body)||[],l={};for(const B of d)l[B]=(l[B]||0)+1;console.log(`${a} 现有:${u} 需要:${(F=(w=t.roleMonitor[a])==null?void 0:w.count)!=null?F:0} Body:${JSON.stringify(l)}`)}),a!=="repairer"?(i=A[a])==null||i.create({body:t.roleMonitor[a].body}):utils.role2[a].create({baseId:E.MainBase,body:t.roleMonitor[a].body});break}},ce=["MinMiner","MinMiner2"],$=["MinPioneer","MinPioneer2","MinPioneer3"],Ee=["MinPioneer5","MinPioneer6"],de=()=>{ge()},ge=()=>{Ce()&&(Se(),fe())},Ce=()=>{const s=[...ce.map(e=>({name:e,role:"miner"})),{name:"MinHarvester",role:"harvester"},{name:"MinHarvester2",role:"harvester"},{name:"MinUpgrader",role:"upgrader"},{name:"MinBuilder",role:"builder"},...$.map(e=>({name:e,role:"pioneer",memoryRoleOpts:{targetRoomName:c.TargetRoomFlag}})),...Ee.map(e=>({name:e,role:"pioneer",memoryRoleOpts:{targetRoomName:c.TargetRoomFlag2}}))],t=Object.values(Game.creeps).filter(e=>e.name.startsWith("MinMiner")).length+Memory.creepsCount.miner;for(const e of s){if(e.name.startsWith("MinMiner")&&t>=2)continue;if(!Game.creeps[e.name]){let n=[];switch(e.role){case"miner":{n=R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:1},{body:MOVE,count:1}]);break}case"pioneer":{n=$.includes(e.name)?R.generatorRoleBody([{body:WORK,count:2},{body:CARRY,count:6},{body:MOVE,count:4}]):R.generatorRoleBody([{body:WORK,count:3},{body:CARRY,count:7},{body:MOVE,count:5}]);break}default:{n=R.generatorRoleBody([{body:WORK,count:1},{body:CARRY,count:2},{body:MOVE,count:2}]);break}}const r=utils.role2[e.role].create({body:n,name:e.name,memoryRoleOpts:e.memoryRoleOpts});switch(r){case OK:{console.log(`MiniGroup 孵化${e.name}成功`);break}case ERR_NOT_ENOUGH_ENERGY:{m(5,()=>console.log(`MiniGroup 缺少${e.name}, 能量不足, 等待孵化`));break}case ERR_NOT_ENOUGH_RESOURCES:{m(5,()=>console.log(`MiniGroup 缺少${e.name}, 资源不足, 等待孵化`));break}default:m(5,()=>console.log(`MiniGroup 孵化${e.name}失败`,r));break}return!1}}return!0},Se=()=>{if(!Memory.creepsCount.miner)return;const s=Memory.creepsCount.miner,t=Object.values(Game.creeps).filter(o=>o.name.startsWith("MinMiner")),e=t.slice(0,2-s);for(const o of t)e.includes(o)||(o.suicide(),console.log(`超过大矿机 miner 上限, 杀死小 MinMiner: ${o.name}`))},Te=()=>{re(),q(),de();const s=Game.rooms[c.MainRoom].find(FIND_HOSTILE_CREEPS);s.length>0&&console.log("有敌人",s)},Oe=()=>{var s,t;for(let e in Game.rooms){const o=Game.rooms[e];if((s=o.controller)!=null&&s.my){Te();for(let n in Game.creeps){let r=Game.creeps[n];r.memory.role&&((t=A[r.memory.role])==null||t.run(r))}X(o)}}};module.exports={loop:Oe};global.utils={role2:A,ticksPerMove:V};
